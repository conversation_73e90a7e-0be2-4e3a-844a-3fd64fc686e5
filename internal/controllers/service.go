package controllers

import (
	"context"
	"fmt"
	"strings"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/service"

	"github.com/gin-gonic/gin"
)

// ServiceController handles API requests for the service node (management layer)
type ServiceController struct {
	service *service.Service
}

// NewServiceController creates a new service controller
func NewServiceController(s *service.Service) *ServiceController {
	return &ServiceController{service: s}
}

// CreateService handles service creation requests
func (c *ServiceController) CreateService(ctx *gin.Context) {
	var req models.CreateServiceRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(400, gin.H{"error": err.Error()})
		return
	}

	resp, err := c.service.CreateDeployRecord(context.Background(), &req)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.<PERSON><PERSON><PERSON>()})
		return
	}

	ctx.JSON(200, resp)
}

// DeleteService handles service deletion requests
func (c *ServiceController) DeleteService(ctx *gin.Context) {
	service_id := ctx.Param("service_id")
	resp, err := c.service.DeleteDeployRecord(context.Background(), service_id)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}

// RestartService handles service restart requests
func (c *ServiceController) RestartService(ctx *gin.Context) {
	service_id := ctx.Param("service_id")
	resp, err := c.service.RestartService(context.Background(), service_id)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}

// UpdateService handles service update requests
func (c *ServiceController) UpdateService(ctx *gin.Context) {
	// 使用专门的更新请求模型，只需要 service_id 和 image_name
	var req models.UpdateServiceRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(400, gin.H{"error": err.Error()})
		return
	}

	resp, err := c.service.UpdateService(context.Background(), &req)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}

// GetServiceList returns a list of services with pagination and filtering
func (c *ServiceController) GetServiceList(ctx *gin.Context) {
	// 解析请求参数
	var req models.DeployRecordListRequest

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		ctx.JSON(400, gin.H{"error": fmt.Sprintf("无效的请求参数: %v", err)})
		return
	}

	// 处理 service_ids 参数（可能是逗号分隔的字符串）
	serviceIDsStr := ctx.Query("service_ids")
	if serviceIDsStr != "" {
		req.ServiceIDs = strings.Split(serviceIDsStr, ",")
	}

	// 调用服务层方法
	resp, err := c.service.GetDeployRecordList(context.Background(), &req)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}

// Worker management methods

// GetWorkerList returns a list of all registered worker nodes with optional filtering
func (c *ServiceController) GetWorkerList(ctx *gin.Context) {
	// 从 URL 查询参数中获取筛选条件
	filter := &models.WorkerFilter{
		ServerType: ctx.Query("server_type"),
		Status:     ctx.Query("status"),
	}

	// 处理标签筛选
	if labelsStr := ctx.Query("labels"); labelsStr != "" {
		// 假设标签以逗号分隔，如 "label1,label2,label3"
		filter.Labels = strings.Split(labelsStr, ",")
	}

	// 如果没有任何筛选条件，则传递 nil
	if filter.ServerType == "" && filter.Status == "" && len(filter.Labels) == 0 {
		filter = nil
	}

	resp, err := c.service.GetWorkerList(context.Background(), filter)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}

// RegisterWorker registers a new worker node with the service
func (c *ServiceController) RegisterWorker(ctx *gin.Context) {
	var req models.RegisterWorkerRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(400, gin.H{"error": err.Error()})
		return
	}

	resp, err := c.service.RegisterWorker(context.Background(), &req)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}

// RemoveWorker removes a worker node from the service
func (c *ServiceController) RemoveWorker(ctx *gin.Context) {
	worker_id := ctx.Param("worker_id")
	resp, err := c.service.RemoveWorker(context.Background(), worker_id)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}

// UpdateWorker updates a worker node
func (c *ServiceController) UpdateWorker(ctx *gin.Context) {
	worker_id := ctx.Param("worker_id")

	var req models.UpdateWorkerRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(400, gin.H{"error": err.Error()})
		return
	}

	resp, err := c.service.UpdateWorker(context.Background(), worker_id, &req)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}

// Image type management methods

// CreateImageTypes handles the creation of one or multiple image types
func (c *ServiceController) CreateImageTypes(ctx *gin.Context) {
	// 尝试绑定为数组
	var reqs []*models.CreateImageTypeRequest
	if err := ctx.ShouldBindJSON(&reqs); err != nil {
		// 如果绑定为数组失败，尝试绑定为单个对象
		var singleReq models.CreateImageTypeRequest
		if err2 := ctx.ShouldBindJSON(&singleReq); err2 != nil {
			ctx.JSON(400, gin.H{"error": "无效的请求格式: " + err.Error() + ", " + err2.Error()})
			return
		}
		// 将单个对象转换为数组
		reqs = []*models.CreateImageTypeRequest{&singleReq}
	}

	// 验证请求数组不为空
	if len(reqs) == 0 {
		ctx.JSON(400, gin.H{"error": "请求不能为空"})
		return
	}

	resp, err := c.service.CreateImageTypes(context.Background(), reqs)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	// 根据响应码返回相应的 HTTP 状态码
	var httpStatus int
	switch resp.Code {
	case 200:
		httpStatus = 200 // OK
	case 207:
		httpStatus = 207 // Multi-Status
	case 400:
		httpStatus = 400 // Bad Request
	case 500:
		httpStatus = 500 // Internal Server Error
	default:
		httpStatus = 200 // 默认为 OK
	}

	ctx.JSON(httpStatus, resp)
}

// GetImageTypeList handles the retrieval of all image types
func (c *ServiceController) GetImageTypeList(ctx *gin.Context) {
	resp, err := c.service.GetImageTypeList(context.Background())
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}

// DeleteImageType handles the deletion of an image type
func (c *ServiceController) DeleteImageType(ctx *gin.Context) {
	id := ctx.Param("id")
	resp, err := c.service.DeleteImageType(context.Background(), id)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}

// UpdateImageType handles the update of an image type
func (c *ServiceController) UpdateImageType(ctx *gin.Context) {
	id := ctx.Param("id")

	var req models.UpdateImageTypeRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(400, gin.H{"error": err.Error()})
		return
	}

	resp, err := c.service.UpdateImageType(context.Background(), id, &req)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}

// CheckResourceStatus 检查指定服务的资源状态
// 支持两种参数：service_ids（向后兼容）和 business_ids（推荐使用）
func (c *ServiceController) CheckResourceStatus(ctx *gin.Context) {
	// 优先检查 business_ids 参数
	businessIDParam := ctx.Query("business_ids")
	serviceIDParam := ctx.Query("service_ids")

	if len(businessIDParam) == 0 && len(serviceIDParam) == 0 {
		ctx.JSON(400, gin.H{"error": "business_ids or service_ids is required"})
		return
	}

	var resp *models.ServiceResponse
	var err error

	if len(businessIDParam) > 0 {
		// 使用 business_ids 参数
		businessIDs := strings.Split(businessIDParam, ",")
		resp, err = c.service.GetResourceStatusByBusinessIDs(context.Background(), businessIDs)
	} else {
		// 使用 service_ids 参数（向后兼容）
		serviceIDs := strings.Split(serviceIDParam, ",")
		resp, err = c.service.GetResourceStatus(context.Background(), serviceIDs)
	}

	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}

// GetServiceWaitingTime 获取服务排队等待时间
func (c *ServiceController) GetServiceWaitingTime(ctx *gin.Context) {
	serviceID := ctx.Query("service_id")
	if serviceID == "" {
		ctx.JSON(400, gin.H{"error": "service_id is required"})
		return
	}

	resp, err := c.service.GetServiceWaitingTime(context.Background(), serviceID)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, resp)
}
