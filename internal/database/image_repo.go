package database

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// ImageTypeRepository handles database operations for image types
type ImageTypeRepository struct {
	db *ServiceDB
}

// NewImageTypeRepository creates a new image type repository
func NewImageTypeRepository(db *ServiceDB) *ImageTypeRepository {
	return &ImageTypeRepository{db: db}
}

// Create adds a new image type to the database
func (r *ImageTypeRepository) Create(imageType *models.ImageType) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// Convert labels to JSON
	labelsJSON, err := json.Marshal(imageType.Labels)
	if err != nil {
		return fmt.Errorf("failed to marshal labels: %w", err)
	}

	// Convert ports to JSON
	portsJSON, err := json.Marshal(imageType.Ports)
	if err != nil {
		return fmt.Errorf("failed to marshal ports: %w", err)
	}

	// 获取当前东八区时间
	now := utils.GetCSTTimeString()

	// Insert the image type
	result, err := r.db.Exec(`
		INSERT INTO image_type (image_name, image_url, ports, labels, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?)
	`, imageType.ImageName, imageType.ImageURL, string(portsJSON), string(labelsJSON), now, now)
	if err != nil {
		return fmt.Errorf("failed to insert image type: %w", err)
	}

	// Get the last inserted ID
	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get last insert ID: %w", err)
	}

	// Update the ID in the struct
	imageType.ID = id
	imageType.CreatedAt = now
	imageType.UpdatedAt = now

	return nil
}

// GetByID retrieves an image type by its ID
func (r *ImageTypeRepository) GetByID(id int64) (models.ImageType, error) {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	var imageType models.ImageType
	var labelsJSON, portsJSON string

	err := r.db.QueryRow(`
		SELECT id, image_name, image_url, ports, labels, created_at, updated_at
		FROM image_type
		WHERE id = ?
	`, id).Scan(&imageType.ID, &imageType.ImageName, &imageType.ImageURL, &portsJSON, &labelsJSON, &imageType.CreatedAt, &imageType.UpdatedAt)
	if err != nil {
		return models.ImageType{}, fmt.Errorf("failed to get image type: %w", err)
	}

	// Parse ports JSON
	if portsJSON != "" {
		if err := json.Unmarshal([]byte(portsJSON), &imageType.Ports); err != nil {
			return models.ImageType{}, fmt.Errorf("failed to unmarshal ports: %w", err)
		}
	}

	// Parse labels JSON
	if labelsJSON != "" {
		if err := json.Unmarshal([]byte(labelsJSON), &imageType.Labels); err != nil {
			return models.ImageType{}, fmt.Errorf("failed to unmarshal labels: %w", err)
		}
	}

	// 转换时间为东八区格式
	imageType.CreatedAt = r.convertToCST(imageType.CreatedAt)
	imageType.UpdatedAt = r.convertToCST(imageType.UpdatedAt)

	return imageType, nil
}

func (r *ImageTypeRepository) GetByName(imageName string) (models.ImageType, error) {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	var imageType models.ImageType
	var labelsJSON, portsJSON string

	err := r.db.QueryRow(`
		SELECT id, image_name, image_url, ports, labels, created_at, updated_at
		FROM image_type
		WHERE image_name = ?
	`, imageName).Scan(&imageType.ID, &imageType.ImageName, &imageType.ImageURL, &portsJSON, &labelsJSON, &imageType.CreatedAt, &imageType.UpdatedAt)
	if err != nil {
		return models.ImageType{}, fmt.Errorf("failed to get image type: %w", err)
	}

	// Parse ports JSON
	if portsJSON != "" {
		if err := json.Unmarshal([]byte(portsJSON), &imageType.Ports); err != nil {
			return models.ImageType{}, fmt.Errorf("failed to unmarshal ports: %w", err)
		}
	}

	// Parse labels JSON
	if labelsJSON != "" {
		if err := json.Unmarshal([]byte(labelsJSON), &imageType.Labels); err != nil {
			return models.ImageType{}, fmt.Errorf("failed to unmarshal labels: %w", err)
		}
	}

	// 转换时间为东八区格式
	imageType.CreatedAt = r.convertToCST(imageType.CreatedAt)
	imageType.UpdatedAt = r.convertToCST(imageType.UpdatedAt)

	return imageType, nil
}

// GetAll retrieves all image types
func (r *ImageTypeRepository) GetAll() ([]models.ImageType, error) {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	rows, err := r.db.Query(`
		SELECT id, image_name, image_url, ports, labels, created_at, updated_at
		FROM image_type
		ORDER BY id DESC
	`)
	if err != nil {
		return nil, fmt.Errorf("failed to query image types: %w", err)
	}
	defer rows.Close()

	var imageTypes []models.ImageType
	for rows.Next() {
		var imageType models.ImageType
		var labelsJSON, portsJSON string

		err := rows.Scan(&imageType.ID, &imageType.ImageName, &imageType.ImageURL, &portsJSON, &labelsJSON, &imageType.CreatedAt, &imageType.UpdatedAt)
		if err != nil {
			return nil, fmt.Errorf("failed to scan image type: %w", err)
		}

		// Parse ports JSON
		if portsJSON != "" {
			if err := json.Unmarshal([]byte(portsJSON), &imageType.Ports); err != nil {
				return nil, fmt.Errorf("failed to unmarshal ports: %w", err)
			}
		}

		// Parse labels JSON
		if labelsJSON != "" {
			if err := json.Unmarshal([]byte(labelsJSON), &imageType.Labels); err != nil {
				return nil, fmt.Errorf("failed to unmarshal labels: %w", err)
			}
		}

		// 转换时间为东八区格式（确保API返回的时间是东八区）
		imageType.CreatedAt = r.convertToCST(imageType.CreatedAt)
		imageType.UpdatedAt = r.convertToCST(imageType.UpdatedAt)

		imageTypes = append(imageTypes, imageType)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating image types: %w", err)
	}

	return imageTypes, nil
}

// Update updates an image type
func (r *ImageTypeRepository) Update(imageType models.ImageType) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	// Convert labels to JSON
	labelsJSON, err := json.Marshal(imageType.Labels)
	if err != nil {
		return fmt.Errorf("failed to marshal labels: %w", err)
	}

	// Convert ports to JSON
	portsJSON, err := json.Marshal(imageType.Ports)
	if err != nil {
		return fmt.Errorf("failed to marshal ports: %w", err)
	}

	// 获取当前东八区时间
	now := utils.GetCSTTimeString()

	// Update the image type
	_, err = r.db.Exec(`
		UPDATE image_type
		SET image_name = ?, image_url = ?, ports = ?, labels = ?, updated_at = ?
		WHERE id = ?
	`, imageType.ImageName, imageType.ImageURL, string(portsJSON), string(labelsJSON), now, imageType.ID)
	if err != nil {
		return fmt.Errorf("failed to update image type: %w", err)
	}

	return nil
}

// Delete removes an image type from the database
func (r *ImageTypeRepository) Delete(id int64) error {
	r.db.mu.Lock()
	defer r.db.mu.Unlock()

	_, err := r.db.Exec("DELETE FROM image_type WHERE id = ?", id)
	if err != nil {
		return fmt.Errorf("failed to delete image type: %w", err)
	}

	return nil
}

// convertToCST 将时间字符串转换为东八区格式
// 支持多种输入格式，统一输出为东八区的RFC3339格式
func (r *ImageTypeRepository) convertToCST(timeStr string) string {
	if timeStr == "" {
		return ""
	}

	// 尝试解析多种时间格式
	formats := []string{
		time.RFC3339,                 // "2006-01-02T15:04:05Z07:00"
		time.RFC3339Nano,             // "2006-01-02T15:04:05.999999999Z07:00"
		"2006-01-02 15:04:05",        // SQLite datetime格式
		"2006-01-02 15:04:05.999999", // SQLite datetime with microseconds
		"2006-01-02T15:04:05",        // ISO8601 without timezone
		"2006-01-02T15:04:05.999999", // ISO8601 with microseconds
	}

	for _, format := range formats {
		if parsedTime, err := time.Parse(format, timeStr); err == nil {
			// 转换为东八区时间并格式化为RFC3339
			return utils.FormatCSTTime(parsedTime)
		}
	}

	// 如果无法解析，返回原字符串
	return timeStr
}
