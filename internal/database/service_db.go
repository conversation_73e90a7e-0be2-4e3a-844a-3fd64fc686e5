package database

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"sync"

	"github.com/zero-ops/service-system/internal/pkg/logger"
	_ "modernc.org/sqlite" // Pure Go SQLite driver
)

// ServiceDB represents a database connection for the service node
type ServiceDB struct {
	*sql.DB
	mu sync.Mutex // Mutex for thread-safe operations
}

// ServiceDBConfig holds configuration for the service database
type ServiceDBConfig struct {
	DBPath string // Path to the database file
}

// DefaultServiceDBConfig returns the default service database configuration
func DefaultServiceDBConfig() ServiceDBConfig {
	return ServiceDBConfig{
		DBPath: filepath.Join("dbdata", "service.db"),
	}
}

// NewServiceDB creates a new service database connection
func NewServiceDB(config ServiceDBConfig) (*ServiceDB, error) {
	// Ensure the directory exists
	dbDir := filepath.Dir(config.DBPath)
	if err := os.Mkdir<PERSON>ll(dbDir, 0755); err != nil {
		logger.Error("Failed to create database directory: %v, path: %s", err, dbDir)
		return nil, fmt.Errorf("failed to create database directory: %w", err)
	}

	// Open the database
	db, err := sql.Open("sqlite", config.DBPath)
	if err != nil {
		logger.Error("Failed to open database: %v, path: %s", err, config.DBPath)
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Test the connection
	if err := db.Ping(); err != nil {
		db.Close()
		logger.Error("Failed to ping database: %v, path: %s", err, config.DBPath)
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// Set pragmas for better performance (WAL mode)
	if _, err := db.Exec("PRAGMA journal_mode=DELETE;"); err != nil {
		db.Close()
		logger.Error("Failed to set journal mode: %v, path: %s", err, config.DBPath)
		return nil, fmt.Errorf("failed to set journal mode: %w", err)
	}

	if _, err := db.Exec("PRAGMA synchronous=NORMAL;"); err != nil {
		db.Close()
		logger.Error("Failed to set synchronous mode: %v, path: %s", err, config.DBPath)
		return nil, fmt.Errorf("failed to set synchronous mode: %w", err)
	}

	// Explicitly disable foreign key constraints
	if _, err := db.Exec("PRAGMA foreign_keys=OFF;"); err != nil {
		db.Close()
		logger.Error("Failed to disable foreign keys: %v, path: %s", err, config.DBPath)
		return nil, fmt.Errorf("failed to disable foreign keys: %w", err)
	}

	return &ServiceDB{DB: db, mu: sync.Mutex{}}, nil
}

// InitSchema initializes the database schema
func (db *ServiceDB) InitSchema() error {
	db.mu.Lock()
	defer db.mu.Unlock()

	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS workers (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			worker_id TEXT NOT NULL,
			server_type TEXT NOT NULL,
			name TEXT NOT NULL,
			labels TEXT, -- 存储为JSON格式的标签数组
			host TEXT NOT NULL, -- api地址
			host_ip TEXT NOT NULL,
			domain_suffix TEXT, -- 基础域名
			nodes TEXT, -- 存储为JSON格式的节点列表
			status TEXT NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		logger.Error("Failed to create workers table: %v", err)
		return fmt.Errorf("failed to create workers table: %w", err)
	}

	// Create deploy_record table
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS deploy_record (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name TEXT NOT NULL,
			service_id TEXT NOT NULL, -- 为用户层创建的唯一id
			domain_prefix TEXT NOT NULL, -- 域名的个性化前缀，后缀由部署环境以及服务类型决定
			domain_suffix TEXT, -- 域名后缀
			image_name TEXT NOT NULL, -- 镜像名称
			service_type TEXT NOT NULL, -- 服务类型
			expiration TEXT NOT NULL, -- YYYY-MM-DDTHH:mm:ss
			duration_seconds INTEGER NOT NULL, -- 服务持续时间（秒）
			labels TEXT, -- 存储为JSON格式的标签数组
			customer_envs TEXT, --- 用户自定义环境变量 存储为JSON格式的数组 ["VARA='some value'", "VARB=123"]
			api_replica INTEGER,
			api_cpu REAL,
			api_memory INTEGER,
			auto_replica INTEGER,
			auto_cpu REAL,
			auto_memory INTEGER,
			status TEXT NOT NULL, -- QUEUEING 排队中|PROCESSING 操作中|RUNNING 运行中|TERMINATION 到期中止|STOPPED 人工停止|FAILED 部署异常
			worker_id TEXT,
			node_ip TEXT,
			host_ip TEXT, -- 存储host节点的ip地址
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			visited_at TIMESTAMP, -- 最后访问时间，用于活动检测
			remark TEXT -- 额外信息
		)
	`)
	if err != nil {
		logger.Error("Failed to create deploy_record table: %v", err)
		return fmt.Errorf("failed to create deploy_record table: %w", err)
	}

	// 创建镜像类型
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS image_type (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			image_name TEXT NOT NULL, -- 镜像名称，需要全局唯一
			image_url TEXT NOT NULL,
			ports  TEXT, -- 存储为JSON格式的端口数组 [] ["8080", "9000"]
			labels TEXT, -- 存储为JSON格式的标签数组
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		logger.Error("Failed to create image_type table: %v", err)
		return fmt.Errorf("failed to create image_type table: %w", err)
	}

	// 创建服务资源使用统计表
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS service_resource_usage (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			service_id TEXT NOT NULL UNIQUE, -- 确保每个服务只有一条记录
			cpu_usage REAL DEFAULT 0.0, -- CPU使用量
			memory_usage INTEGER DEFAULT 0, -- 内存使用量 (MB)
			disk_usage INTEGER DEFAULT 0, -- 磁盘使用量 (MB)
			oss_disk_usage INTEGER DEFAULT 0, -- OSS磁盘使用量 (MB)
			oss_network_traffic REAL DEFAULT 0.0, -- OSS网络流量 (MB)
			oss_network_traffic_by_day REAL DEFAULT 0.0, -- OSS网络流量前一天 (MB)
			network_in_traffic REAL DEFAULT 0.0, -- 入网流量 (MB)
			network_out_traffic REAL DEFAULT 0.0, -- 出网流量 (MB)
			network_traffic_updated_at TIMESTAMP, -- 上次更新流量的时间
			last_access_time TIMESTAMP, -- 最后访问时间
			metadata TEXT, -- 扩展元数据 (JSON格式)
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		logger.Error("Failed to create service_resource_usage table: %v", err)
		return fmt.Errorf("failed to create service_resource_usage table: %w", err)
	}

	// 创建索引以提高查询性能
	_, err = db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_service_resource_usage_updated_at
		ON service_resource_usage(updated_at)
	`)
	if err != nil {
		logger.Error("Failed to create service_resource_usage updated_at index: %v", err)
		return fmt.Errorf("failed to create service_resource_usage updated_at index: %w", err)
	}

	// 检查并添加新字段 oss_network_traffic_by_day（如果不存在）
	_, err = db.Exec(`
		ALTER TABLE service_resource_usage
		ADD COLUMN oss_network_traffic_by_day REAL DEFAULT 0.0
	`)
	if err != nil {
		// 如果字段已存在，ALTER TABLE 会失败，这是正常的
		logger.Info("Adding oss_network_traffic_by_day column: %v (column may already exist)", err)
	} else {
		logger.Info("Successfully added oss_network_traffic_by_day column")
	}

	return nil
}

// Close closes the database connection
func (db *ServiceDB) Close() error {
	return db.DB.Close()
}
