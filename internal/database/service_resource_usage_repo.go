package database

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/logger"
)

// parseTimeFromDB 解析数据库中的时间字符串
// 支持多种时间格式：RFC3339, SQLite datetime格式等
func parseTimeFromDB(timeStr string) (*time.Time, error) {
	if timeStr == "" {
		return nil, nil
	}

	// 尝试不同的时间格式
	formats := []string{
		time.RFC3339,                 // "2006-01-02T15:04:05Z07:00"
		time.RFC3339Nano,             // "2006-01-02T15:04:05.999999999Z07:00"
		"2006-01-02 15:04:05",        // SQLite datetime格式
		"2006-01-02 15:04:05.999999", // SQLite datetime with microseconds
		"2006-01-02T15:04:05",        // ISO8601 without timezone
		"2006-01-02T15:04:05.999999", // ISO8601 with microseconds
	}

	for _, format := range formats {
		if parsedTime, err := time.Parse(format, timeStr); err == nil {
			return &parsedTime, nil
		}
	}

	return nil, fmt.Errorf("无法解析时间格式: %s", timeStr)
}

// ServiceResourceUsageRepository 服务资源使用统计数据库操作
type ServiceResourceUsageRepository struct {
	db *ServiceDB
}

// NewServiceResourceUsageRepository 创建新的资源使用统计仓库
func NewServiceResourceUsageRepository(db *ServiceDB) *ServiceResourceUsageRepository {
	return &ServiceResourceUsageRepository{db: db}
}

// GetByServiceID 通过service_id获取资源使用统计
func (r *ServiceResourceUsageRepository) GetByServiceID(serviceID string) (*models.ServiceResourceUsage, error) {
	logger.Info("获取服务资源使用统计: serviceID=%s", serviceID)

	query := `
		SELECT id, service_id, cpu_usage, memory_usage, disk_usage, oss_disk_usage,
		       oss_network_traffic, oss_network_traffic_by_day, network_in_traffic, network_out_traffic,
		       network_traffic_updated_at, last_access_time, metadata, created_at, updated_at
		FROM service_resource_usage
		WHERE service_id = ?
	`

	row := r.db.QueryRow(query, serviceID)

	usage := &models.ServiceResourceUsage{}
	var networkTrafficUpdatedAt, lastAccessTime sql.NullString
	var createdAtStr, updatedAtStr string

	err := row.Scan(
		&usage.ID, &usage.ServiceID, &usage.CPUUsage, &usage.MemoryUsage,
		&usage.DiskUsage, &usage.OSSDiskUsage, &usage.OSSNetworkTraffic, &usage.OSSNetworkTrafficByDay,
		&usage.NetworkInTraffic, &usage.NetworkOutTraffic,
		&networkTrafficUpdatedAt, &lastAccessTime, &usage.Metadata, &createdAtStr, &updatedAtStr,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			logger.Info("未找到服务资源使用统计: serviceID=%s", serviceID)
			return nil, nil
		}
		logger.Error("查询服务资源使用统计失败: %v", err)
		return nil, fmt.Errorf("查询服务资源使用统计失败: %w", err)
	}

	// 解析时间字段
	if networkTrafficUpdatedAt.Valid && networkTrafficUpdatedAt.String != "" {
		if parsedTime, err := parseTimeFromDB(networkTrafficUpdatedAt.String); err == nil && parsedTime != nil {
			usage.NetworkTrafficUpdatedAt = parsedTime
		} else {
			logger.Warn("解析 NetworkTrafficUpdatedAt 失败: %v, 值: %s", err, networkTrafficUpdatedAt.String)
		}
	}
	if lastAccessTime.Valid && lastAccessTime.String != "" {
		if parsedTime, err := parseTimeFromDB(lastAccessTime.String); err == nil && parsedTime != nil {
			usage.LastAccessTime = parsedTime
		} else {
			logger.Warn("解析 LastAccessTime 失败: %v, 值: %s", err, lastAccessTime.String)
		}
	}

	// 解析 CreatedAt 和 UpdatedAt
	if parsedTime, err := parseTimeFromDB(createdAtStr); err == nil && parsedTime != nil {
		usage.CreatedAt = *parsedTime
	} else {
		logger.Warn("解析 CreatedAt 失败: %v, 值: %s", err, createdAtStr)
	}
	if parsedTime, err := parseTimeFromDB(updatedAtStr); err == nil && parsedTime != nil {
		usage.UpdatedAt = *parsedTime
	} else {
		logger.Warn("解析 UpdatedAt 失败: %v, 值: %s", err, updatedAtStr)
	}

	logger.Info("成功获取服务资源使用统计: serviceID=%s", serviceID)
	return usage, nil
}

// GetByServiceIDs 批量通过service_id获取资源使用统计
func (r *ServiceResourceUsageRepository) GetByServiceIDs(serviceIDs []string) (map[string]*models.ServiceResourceUsage, error) {
	logger.Info("批量获取服务资源使用统计: serviceIDs=%v", serviceIDs)

	if len(serviceIDs) == 0 {
		return make(map[string]*models.ServiceResourceUsage), nil
	}

	// 构建 IN 查询的占位符
	placeholders := make([]string, len(serviceIDs))
	args := make([]interface{}, len(serviceIDs))
	for i, serviceID := range serviceIDs {
		placeholders[i] = "?"
		args[i] = serviceID
	}

	query := fmt.Sprintf(`
		SELECT id, service_id, cpu_usage, memory_usage, disk_usage, oss_disk_usage,
		       oss_network_traffic, oss_network_traffic_by_day, network_in_traffic, network_out_traffic,
		       network_traffic_updated_at, last_access_time, metadata, created_at, updated_at
		FROM service_resource_usage
		WHERE service_id IN (%s)
	`, strings.Join(placeholders, ","))

	rows, err := r.db.Query(query, args...)
	if err != nil {
		logger.Error("批量查询服务资源使用统计失败: %v", err)
		return nil, fmt.Errorf("批量查询服务资源使用统计失败: %w", err)
	}
	defer rows.Close()

	result := make(map[string]*models.ServiceResourceUsage)
	rowCount := 0
	for rows.Next() {
		rowCount++
		usage := &models.ServiceResourceUsage{}
		var networkTrafficUpdatedAt, lastAccessTime sql.NullString
		var createdAtStr, updatedAtStr string

		err := rows.Scan(
			&usage.ID, &usage.ServiceID, &usage.CPUUsage, &usage.MemoryUsage,
			&usage.DiskUsage, &usage.OSSDiskUsage, &usage.OSSNetworkTraffic, &usage.OSSNetworkTrafficByDay,
			&usage.NetworkInTraffic, &usage.NetworkOutTraffic,
			&networkTrafficUpdatedAt, &lastAccessTime, &usage.Metadata, &createdAtStr, &updatedAtStr,
		)
		if err != nil {
			logger.Error("扫描服务资源使用统计失败: %v", err)
			return nil, fmt.Errorf("扫描服务资源使用统计失败: %w", err)
		}

		// 解析时间字段
		if networkTrafficUpdatedAt.Valid && networkTrafficUpdatedAt.String != "" {
			if parsedTime, err := parseTimeFromDB(networkTrafficUpdatedAt.String); err == nil && parsedTime != nil {
				usage.NetworkTrafficUpdatedAt = parsedTime
			} else {
				logger.Warn("解析 NetworkTrafficUpdatedAt 失败: %v, 值: %s", err, networkTrafficUpdatedAt.String)
			}
		}
		if lastAccessTime.Valid && lastAccessTime.String != "" {
			if parsedTime, err := parseTimeFromDB(lastAccessTime.String); err == nil && parsedTime != nil {
				usage.LastAccessTime = parsedTime
			} else {
				logger.Warn("解析 LastAccessTime 失败: %v, 值: %s", err, lastAccessTime.String)
			}
		}

		// 解析 CreatedAt 和 UpdatedAt
		if parsedTime, err := parseTimeFromDB(createdAtStr); err == nil && parsedTime != nil {
			usage.CreatedAt = *parsedTime
		} else {
			logger.Warn("解析 CreatedAt 失败: %v, 值: %s", err, createdAtStr)
		}
		if parsedTime, err := parseTimeFromDB(updatedAtStr); err == nil && parsedTime != nil {
			usage.UpdatedAt = *parsedTime
		} else {
			logger.Warn("解析 UpdatedAt 失败: %v, 值: %s", err, updatedAtStr)
		}

		result[usage.ServiceID] = usage
	}

	if err = rows.Err(); err != nil {
		logger.Error("遍历服务资源使用统计结果失败: %v", err)
		return nil, fmt.Errorf("遍历服务资源使用统计结果失败: %w", err)
	}

	logger.Info("成功批量获取服务资源使用统计: 查询=%d, 找到=%d", len(serviceIDs), len(result))
	return result, nil
}

// Create 创建新的资源使用统计记录
func (r *ServiceResourceUsageRepository) Create(usage *models.ServiceResourceUsage) error {
	logger.Info("创建服务资源使用统计: serviceID=%s", usage.ServiceID)

	query := `
		INSERT INTO service_resource_usage (
			service_id, cpu_usage, memory_usage, disk_usage, oss_disk_usage,
			oss_network_traffic, oss_network_traffic_by_day, network_in_traffic, network_out_traffic,
			network_traffic_updated_at, last_access_time, metadata, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	var networkTrafficUpdatedAt, lastAccessTime interface{}
	if usage.NetworkTrafficUpdatedAt != nil {
		networkTrafficUpdatedAt = usage.NetworkTrafficUpdatedAt
	}
	if usage.LastAccessTime != nil {
		lastAccessTime = usage.LastAccessTime
	}

	now := time.Now()
	usage.CreatedAt = now
	usage.UpdatedAt = now

	result, err := r.db.Exec(query,
		usage.ServiceID, usage.CPUUsage, usage.MemoryUsage, usage.DiskUsage,
		usage.OSSDiskUsage, usage.OSSNetworkTraffic, usage.OSSNetworkTrafficByDay, usage.NetworkInTraffic,
		usage.NetworkOutTraffic, networkTrafficUpdatedAt, lastAccessTime, usage.Metadata,
		usage.CreatedAt, usage.UpdatedAt,
	)

	if err != nil {
		logger.Error("创建服务资源使用统计失败: %v", err)
		return fmt.Errorf("创建服务资源使用统计失败: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		logger.Error("获取插入ID失败: %v", err)
		return fmt.Errorf("获取插入ID失败: %w", err)
	}

	usage.ID = id
	logger.Info("成功创建服务资源使用统计: ID=%d, serviceID=%s", id, usage.ServiceID)
	return nil
}

// UpdateByServiceID 通过service_id更新资源使用统计
func (r *ServiceResourceUsageRepository) UpdateByServiceID(serviceID string, usage *models.ServiceResourceUsage) error {
	logger.Info("更新服务资源使用统计: serviceID=%s", serviceID)

	// 构建动态更新语句
	var setParts []string
	var args []interface{}

	if usage.CPUUsage != 0 {
		setParts = append(setParts, "cpu_usage = ?")
		args = append(args, usage.CPUUsage)
	}
	if usage.MemoryUsage != 0 {
		setParts = append(setParts, "memory_usage = ?")
		args = append(args, usage.MemoryUsage)
	}
	if usage.DiskUsage != 0 {
		setParts = append(setParts, "disk_usage = ?")
		args = append(args, usage.DiskUsage)
	}
	if usage.OSSDiskUsage != 0 {
		setParts = append(setParts, "oss_disk_usage = ?")
		args = append(args, usage.OSSDiskUsage)
	}
	if usage.OSSNetworkTraffic != 0 {
		setParts = append(setParts, "oss_network_traffic = ?")
		args = append(args, usage.OSSNetworkTraffic)
	}
	if usage.OSSNetworkTrafficByDay != 0 {
		setParts = append(setParts, "oss_network_traffic_by_day = ?")
		args = append(args, usage.OSSNetworkTrafficByDay)
	}
	if usage.NetworkInTraffic != 0.0 {
		setParts = append(setParts, "network_in_traffic = ?")
		args = append(args, usage.NetworkInTraffic)
	}
	if usage.NetworkOutTraffic != 0.0 {
		setParts = append(setParts, "network_out_traffic = ?")
		args = append(args, usage.NetworkOutTraffic)
	}
	if usage.NetworkTrafficUpdatedAt != nil {
		setParts = append(setParts, "network_traffic_updated_at = ?")
		args = append(args, usage.NetworkTrafficUpdatedAt)
	}
	if usage.LastAccessTime != nil {
		setParts = append(setParts, "last_access_time = ?")
		args = append(args, usage.LastAccessTime)
	}
	// 总是更新 metadata，即使为空字符串（允许清空元数据）
	setParts = append(setParts, "metadata = ?")
	args = append(args, usage.Metadata)

	// 总是更新 updated_at
	setParts = append(setParts, "updated_at = ?")
	args = append(args, time.Now())

	if len(setParts) == 1 { // 只有 updated_at
		logger.Warn("没有需要更新的字段: serviceID=%s", serviceID)
		return nil
	}

	// 添加 WHERE 条件参数
	args = append(args, serviceID)

	query := fmt.Sprintf(`
		UPDATE service_resource_usage
		SET %s
		WHERE service_id = ?
	`, strings.Join(setParts, ", "))

	result, err := r.db.Exec(query, args...)
	if err != nil {
		logger.Error("更新服务资源使用统计失败: %v", err)
		return fmt.Errorf("更新服务资源使用统计失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error("获取更新行数失败: %v", err)
		return fmt.Errorf("获取更新行数失败: %w", err)
	}

	if rowsAffected == 0 {
		logger.Warn("没有找到要更新的记录: serviceID=%s", serviceID)
		return fmt.Errorf("没有找到要更新的记录: serviceID=%s", serviceID)
	}

	logger.Info("成功更新服务资源使用统计: serviceID=%s, rowsAffected=%d", serviceID, rowsAffected)
	return nil
}

// UpsertByServiceID 通过service_id创建或更新资源使用统计
func (r *ServiceResourceUsageRepository) UpsertByServiceID(usage *models.ServiceResourceUsage) error {
	logger.Info("创建或更新服务资源使用统计: serviceID=%s", usage.ServiceID)

	// 先尝试获取现有记录
	existing, err := r.GetByServiceID(usage.ServiceID)
	if err != nil {
		return fmt.Errorf("检查现有记录失败: %w", err)
	}

	if existing == nil {
		// 记录不存在，创建新记录
		return r.Create(usage)
	} else {
		// 记录存在，更新记录
		return r.UpdateByServiceID(usage.ServiceID, usage)
	}
}

// DeleteByServiceID 删除指定服务的资源使用统计
func (r *ServiceResourceUsageRepository) DeleteByServiceID(serviceID string) error {
	logger.Info("删除服务资源使用统计: serviceID=%s", serviceID)

	query := `DELETE FROM service_resource_usage WHERE service_id = ?`

	result, err := r.db.Exec(query, serviceID)
	if err != nil {
		logger.Error("删除服务资源使用统计失败: %v", err)
		return fmt.Errorf("删除服务资源使用统计失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error("获取删除行数失败: %v", err)
		return fmt.Errorf("获取删除行数失败: %w", err)
	}

	logger.Info("成功删除服务资源使用统计: serviceID=%s, rowsAffected=%d", serviceID, rowsAffected)
	return nil
}

// GetAll 获取所有资源使用统计
func (r *ServiceResourceUsageRepository) GetAll(limit int, offset int) ([]*models.ServiceResourceUsage, error) {
	logger.Info("获取所有资源使用统计: limit=%d, offset=%d", limit, offset)

	query := `
		SELECT id, service_id, cpu_usage, memory_usage, disk_usage, oss_disk_usage,
		       oss_network_traffic, oss_network_traffic_by_day, network_in_traffic, network_out_traffic,
		       network_traffic_updated_at, last_access_time, metadata, created_at, updated_at
		FROM service_resource_usage
		ORDER BY updated_at DESC
		LIMIT ? OFFSET ?
	`

	rows, err := r.db.Query(query, limit, offset)
	if err != nil {
		logger.Error("查询所有资源使用统计失败: %v", err)
		return nil, fmt.Errorf("查询所有资源使用统计失败: %w", err)
	}
	defer rows.Close()

	var usages []*models.ServiceResourceUsage
	for rows.Next() {
		usage := &models.ServiceResourceUsage{}
		var networkTrafficUpdatedAt, lastAccessTime sql.NullString
		var createdAtStr, updatedAtStr string

		err := rows.Scan(
			&usage.ID, &usage.ServiceID, &usage.CPUUsage, &usage.MemoryUsage,
			&usage.DiskUsage, &usage.OSSDiskUsage, &usage.OSSNetworkTraffic, &usage.OSSNetworkTrafficByDay,
			&usage.NetworkInTraffic, &usage.NetworkOutTraffic,
			&networkTrafficUpdatedAt, &lastAccessTime, &usage.Metadata, &createdAtStr, &updatedAtStr,
		)
		if err != nil {
			logger.Error("扫描资源使用统计失败: %v", err)
			return nil, fmt.Errorf("扫描资源使用统计失败: %w", err)
		}

		// 解析时间字段
		if networkTrafficUpdatedAt.Valid && networkTrafficUpdatedAt.String != "" {
			if parsedTime, err := parseTimeFromDB(networkTrafficUpdatedAt.String); err == nil && parsedTime != nil {
				usage.NetworkTrafficUpdatedAt = parsedTime
			} else {
				logger.Warn("解析 NetworkTrafficUpdatedAt 失败: %v, 值: %s", err, networkTrafficUpdatedAt.String)
			}
		}
		if lastAccessTime.Valid && lastAccessTime.String != "" {
			if parsedTime, err := parseTimeFromDB(lastAccessTime.String); err == nil && parsedTime != nil {
				usage.LastAccessTime = parsedTime
			} else {
				logger.Warn("解析 LastAccessTime 失败: %v, 值: %s", err, lastAccessTime.String)
			}
		}

		// 解析 CreatedAt 和 UpdatedAt
		if parsedTime, err := parseTimeFromDB(createdAtStr); err == nil && parsedTime != nil {
			usage.CreatedAt = *parsedTime
		} else {
			logger.Warn("解析 CreatedAt 失败: %v, 值: %s", err, createdAtStr)
		}
		if parsedTime, err := parseTimeFromDB(updatedAtStr); err == nil && parsedTime != nil {
			usage.UpdatedAt = *parsedTime
		} else {
			logger.Warn("解析 UpdatedAt 失败: %v, 值: %s", err, updatedAtStr)
		}

		usages = append(usages, usage)
	}

	if err = rows.Err(); err != nil {
		logger.Error("遍历资源使用统计结果失败: %v", err)
		return nil, fmt.Errorf("遍历资源使用统计结果失败: %w", err)
	}

	logger.Info("成功获取所有资源使用统计: count=%d", len(usages))
	return usages, nil
}
