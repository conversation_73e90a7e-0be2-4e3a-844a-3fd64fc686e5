package models

import (
	"encoding/json"
	"time"
)

// ServiceResourceUsage 服务资源使用统计模型
type ServiceResourceUsage struct {
	ID                      int64      `json:"id" db:"id"`
	ServiceID               string     `json:"service_id" db:"service_id"`
	CPUUsage                float64    `json:"cpu_usage" db:"cpu_usage"`                                   // CPU使用率 (0.0-100.0)
	MemoryUsage             int64      `json:"memory_usage" db:"memory_usage"`                             // 内存使用量 (MB)
	DiskUsage               int64      `json:"disk_usage" db:"disk_usage"`                                 // 磁盘使用量 (MB)
	OSSDiskUsage            int64      `json:"oss_disk_usage" db:"oss_disk_usage"`                         // OSS磁盘使用量 (MB)
	OSSNetworkTraffic       float64    `json:"oss_network_traffic" db:"oss_network_traffic"`               // OSS网络流量 (MB)
	OSSNetworkTrafficByDay  float64    `json:"oss_network_traffic_by_day" db:"oss_network_traffic_by_day"` // OSS网络流量前一天 (MB)
	NetworkInTraffic        float64    `json:"network_in_traffic" db:"network_in_traffic"`                 // 入网流量 (MB)
	NetworkOutTraffic       float64    `json:"network_out_traffic" db:"network_out_traffic"`               // 出网流量 (MB)
	NetworkTrafficUpdatedAt *time.Time `json:"network_traffic_updated_at" db:"network_traffic_updated_at"` // 上次更新流量的时间
	LastAccessTime          *time.Time `json:"last_access_time" db:"last_access_time"`                     // 最后访问时间
	Metadata                string     `json:"metadata" db:"metadata"`                                     // 扩展元数据 (JSON格式)
	CreatedAt               time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt               time.Time  `json:"updated_at" db:"updated_at"`
}

// ResourceUsageMetadata 资源使用元数据结构
type ResourceUsageMetadata struct {
	// OSS相关
	OSSBucket       string `json:"oss_bucket,omitempty"`
	OSSEndpoint     string `json:"oss_endpoint,omitempty"`
	OSSRegion       string `json:"oss_region,omitempty"`
	OSSFilePath     string `json:"oss_file_path,omitempty"`
	OSSFileCount    int64  `json:"oss_file_count,omitempty"`
	OSSLastModified string `json:"oss_last_modified,omitempty"`

	// 容器相关
	ContainerIDs []string `json:"container_ids,omitempty"`
	ImageName    string   `json:"image_name,omitempty"`
	NodeIP       string   `json:"node_ip,omitempty"`
	HostIP       string   `json:"host_ip,omitempty"`

	// 网络相关
	DomainPrefix string   `json:"domain_prefix,omitempty"`
	DomainSuffix string   `json:"domain_suffix,omitempty"`
	Ports        []string `json:"ports,omitempty"`

	// 统计相关
	CollectionTime time.Time `json:"collection_time,omitempty"`
	ExecutionTime  string    `json:"execution_time,omitempty"`
	DataSources    []string  `json:"data_sources,omitempty"`
	Errors         []string  `json:"errors,omitempty"`

	// 扩展字段
	CustomFields map[string]interface{} `json:"custom_fields,omitempty"`
}

// SetMetadata 设置元数据
func (s *ServiceResourceUsage) SetMetadata(metadata *ResourceUsageMetadata) error {
	if metadata == nil {
		s.Metadata = ""
		return nil
	}

	data, err := json.Marshal(metadata)
	if err != nil {
		return err
	}

	s.Metadata = string(data)
	return nil
}

// GetMetadata 获取元数据
func (s *ServiceResourceUsage) GetMetadata() (*ResourceUsageMetadata, error) {
	if s.Metadata == "" {
		return &ResourceUsageMetadata{}, nil
	}

	var metadata ResourceUsageMetadata
	err := json.Unmarshal([]byte(s.Metadata), &metadata)
	if err != nil {
		return nil, err
	}

	return &metadata, nil
}

// GetTotalNetworkTraffic 获取总网络流量 (MB)
func (s *ServiceResourceUsage) GetTotalNetworkTraffic() float64 {
	return s.NetworkInTraffic + s.NetworkOutTraffic + s.OSSNetworkTraffic
}

// GetTotalDiskUsage 获取总磁盘使用量 (MB)
func (s *ServiceResourceUsage) GetTotalDiskUsage() int64 {
	return s.DiskUsage + s.OSSDiskUsage
}

// ServiceResourceUsageResponse 资源使用统计响应结构
type ServiceResourceUsageResponse struct {
	*ServiceResourceUsage
	Metadata *ResourceUsageMetadata `json:"metadata_parsed,omitempty"` // 解析后的元数据
}

// NewServiceResourceUsageResponse 创建响应结构
func NewServiceResourceUsageResponse(usage *ServiceResourceUsage) (*ServiceResourceUsageResponse, error) {
	resp := &ServiceResourceUsageResponse{
		ServiceResourceUsage: usage,
	}

	// 解析元数据
	if usage.Metadata != "" {
		metadata, err := usage.GetMetadata()
		if err != nil {
			return nil, err
		}
		resp.Metadata = metadata
	}

	return resp, nil
}

// ResourceUsageSummary 资源使用统计汇总
type ResourceUsageSummary struct {
	StatsType              string  `json:"stats_type"`
	ServiceCount           int64   `json:"service_count"`
	AvgCPUUsage            float64 `json:"avg_cpu_usage"`
	TotalMemoryUsage       int64   `json:"total_memory_usage"`
	TotalDiskUsage         int64   `json:"total_disk_usage"`
	TotalOSSDiskUsage      int64   `json:"total_oss_disk_usage"`
	TotalOSSNetworkTraffic float64 `json:"total_oss_network_traffic"`
	TotalNetworkInTraffic  float64 `json:"total_network_in_traffic"`
	TotalNetworkOutTraffic float64 `json:"total_network_out_traffic"`
	TotalContainerCount    int64   `json:"total_container_count"`
	MaxCPUUsage            float64 `json:"max_cpu_usage"`
	MaxMemoryUsage         int64   `json:"max_memory_usage"`
	MaxDiskUsage           int64   `json:"max_disk_usage"`
	MaxOSSDiskUsage        int64   `json:"max_oss_disk_usage"`
}

// GetTotalNetworkTraffic 获取总网络流量
func (s *ResourceUsageSummary) GetTotalNetworkTraffic() float64 {
	return s.TotalNetworkInTraffic + s.TotalNetworkOutTraffic + s.TotalOSSNetworkTraffic
}

// GetTotalDiskUsage 获取总磁盘使用量
func (s *ResourceUsageSummary) GetTotalDiskUsage() int64 {
	return s.TotalDiskUsage + s.TotalOSSDiskUsage
}

// OSSUsageStats OSS 使用统计（用于 OSSManager）
type OSSUsageStats struct {
	ServiceID       string    `json:"service_id"`
	DiskUsageMB     int64     `json:"disk_usage_mb"`     // OSS 磁盘使用量 (MB)
	FileCount       int64     `json:"file_count"`        // 文件数量
	TrafficUsageMB  float64   `json:"traffic_usage_mb"`  // 流量使用量 (MB)
	CheckTime       time.Time `json:"check_time"`        // 检查时间
	DataSource      string    `json:"data_source"`       // 数据来源 (api/ossutil)
	ExecutionTimeMs int64     `json:"execution_time_ms"` // 执行时间 (毫秒)
}
