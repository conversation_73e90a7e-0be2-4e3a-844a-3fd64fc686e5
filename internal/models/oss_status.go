package models

import "time"

// OSSConfigInfo OSS 配置信息
type OSSConfigInfo struct {
	Bucket   string `json:"bucket"`    // 存储桶名称
	Endpoint string `json:"endpoint"`  // OSS 端点
	Region   string `json:"region"`    // 区域
	FilePath string `json:"file_path"` // 文件路径
}

// 注意：OSSAPIStats 已删除，现在只使用 OSSUtil 方式

// OSSUtilStats OSSUtil 工具统计信息
type OSSUtilStats struct {
	TotalSize     int64   `json:"total_size"`     // 总大小（字节）
	TotalSizeMB   float64 `json:"total_size_mb"`  // 总大小（MB）
	FileCount     int64   `json:"file_count"`     // 文件数量
	ObjectCount   int64   `json:"object_count"`   // 对象数量
	BucketPath    string  `json:"bucket_path"`    // bucket 路径
	ExecutionTime string  `json:"execution_time"` // 执行时间
}

// OSSStatusResult OSS 状态检查结果（重构版）
type OSSStatusResult struct {
	ServiceID    string         `json:"service_id"`
	OSSConfig    *OSSConfigInfo `json:"oss_config"`                  // 类型安全的配置信息
	OSSUtilStats *OSSUtilStats  `json:"oss_stats_ossutil,omitempty"` // 类型安全的 OSSUtil 统计
	CheckTime    time.Time      `json:"check_time"`
	Success      bool           `json:"success"`
	ErrorMessage string         `json:"error_message,omitempty"`
}

// GetTotalSizeMB 获取总大小（MB）- 统一接口
func (u *OSSUtilStats) GetTotalSizeMB() float64 {
	return u.TotalSizeMB
}

// GetFileCount 获取文件数量 - 统一接口
func (u *OSSUtilStats) GetFileCount() int64 {
	return u.ObjectCount // OSSUtil 使用 ObjectCount
}

// GetStatsType 获取统计类型 - 统一接口
func (u *OSSUtilStats) GetStatsType() string {
	return "ossutil"
}

// 注意：兼容性转换方法已删除，现在直接使用类型安全的结构体
