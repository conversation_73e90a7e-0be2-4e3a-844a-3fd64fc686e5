package models

import (
	"time"
)

// OSSTrafficConfig OSS 流量统计配置
type OSSTrafficConfig struct {
	// 基础配置
	Enabled         bool          `json:"enabled" yaml:"enabled"`
	ProcessInterval time.Duration `json:"process_interval" yaml:"process_interval"` // 处理间隔
	DelayOffset     time.Duration `json:"delay_offset" yaml:"delay_offset"`         // 延迟偏移
	BatchSize       int           `json:"batch_size" yaml:"batch_size"`             // 批处理大小（小时数）

	// 高级配置
	MaxRetryHours    int    `json:"max_retry_hours" yaml:"max_retry_hours"`       // 最大重试小时数
	ConcurrentLimit  int    `json:"concurrent_limit" yaml:"concurrent_limit"`     // 并发处理限制
	TempDir          string `json:"temp_dir" yaml:"temp_dir"`                     // 临时文件目录
	CleanupAfterDays int    `json:"cleanup_after_days" yaml:"cleanup_after_days"` // 清理天数
}

// OSSLogEntry OSS 日志记录
type OSSLogEntry struct {
	Timestamp   time.Time `json:"timestamp"`
	ClientIP    string    `json:"client_ip"`
	Method      string    `json:"method"`
	FilePath    string    `json:"file_path"`
	StatusCode  int       `json:"status_code"`
	ObjectSize  int64     `json:"object_size"`  // 文件大小（字节）
	ServiceID   string    `json:"service_id"`   // 从路径提取的 service_id
	ProcessTime int64     `json:"process_time"` // 处理时间（毫秒）
}

// OSSTrafficStats OSS 流量统计结果
type OSSTrafficStats struct {
	ServiceID       string    `json:"service_id"`
	TotalDownloadMB float64   `json:"total_download_mb"`
	FileCount       int64     `json:"file_count"`
	RequestCount    int64     `json:"request_count"`
	TimeRange       TimeRange `json:"time_range"`
	ProcessTime     time.Time `json:"process_time"`
}

// TimeRange 时间范围
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// OSSLogProcessResult OSS 日志处理结果
type OSSLogProcessResult struct {
	Success        bool                        `json:"success"`
	ProcessedFiles []string                    `json:"processed_files"`
	TrafficStats   map[string]*OSSTrafficStats `json:"traffic_stats"` // key: serviceID
	ErrorMessage   string                      `json:"error_message"`
	ProcessTime    time.Time                   `json:"process_time"`
	TimeRange      TimeRange                   `json:"time_range"`
}
