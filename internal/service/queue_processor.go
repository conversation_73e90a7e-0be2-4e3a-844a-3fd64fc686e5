package service

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/interfaces"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// DeployRecordWithTrafficInfo 增强版部署记录，专门用于流量统计处理
// 包含原始部署记录信息以及流量统计相关的额外信息
type DeployRecordWithTrafficInfo struct {
	models.DeployRecord
	NetworkTrafficUpdatedAt *time.Time // 上次流量更新时间
	CurrentOSSTraffic       float64    // 当前OSS流量(MB)
}

// ProcessingGroup 流量统计处理分组
// 按照 NetworkTrafficUpdatedAt 进行分组，支持多天日志回溯处理
type ProcessingGroup struct {
	StartDate time.Time                     // 开始处理的日期
	Records   []DeployRecordWithTrafficInfo // 该组的记录
}

// ServiceTrafficAccumulator 服务流量累加器
// 支持总流量和前一天流量记录
type ServiceTrafficAccumulator struct {
	totalTraffic       map[string]float64 // serviceID -> 累计总流量(MB)
	previousDayTraffic map[string]float64 // serviceID -> 前一天流量(MB)
	mu                 sync.RWMutex       // 并发安全锁
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// getWorkerLock 获取指定worker的专用锁，用于避免并发资源更新冲突
// 使用双重检查锁定模式确保线程安全，避免死锁
func (s *Service) getWorkerLock(workerID string) *sync.Mutex {
	if workerID == "" {
		// 对于空workerID，返回一个临时锁，避免panic
		return &sync.Mutex{}
	}

	// 第一次检查：使用读锁尝试获取已存在的锁
	s.workerLocksMu.RLock()
	if lock, exists := s.workerLocks[workerID]; exists {
		s.workerLocksMu.RUnlock()
		return lock
	}
	s.workerLocksMu.RUnlock()

	// 如果不存在，获取写锁创建新锁
	s.workerLocksMu.Lock()
	defer s.workerLocksMu.Unlock()

	// 双重检查：避免在等待写锁期间其他goroutine已经创建了锁
	if lock, exists := s.workerLocks[workerID]; exists {
		return lock
	}

	// 创建新锁并存储
	s.workerLocks[workerID] = &sync.Mutex{}
	log.Printf("Created new worker lock for worker: %s", workerID)
	return s.workerLocks[workerID]
}

// StartQueueProcessor 启动队列处理器，使用独立的 goroutine 处理队列和更新状态
func (s *Service) StartQueueProcessor(ctx context.Context) {
	// 使用互斥锁保护状态检查和修改
	s.queueProcessorMu.Lock()
	defer s.queueProcessorMu.Unlock()

	// 检查是否已经在运行
	if s.queueProcessorRunning {
		log.Println("Queue processor is already running, ignoring start request")
		return
	}

	log.Println("Starting queue processor...")

	// 创建可取消的上下文
	processorCtx, cancel := context.WithCancel(ctx)
	s.queueProcessorCancel = cancel
	s.queueProcessorRunning = true

	// 启动一个独立的协程来管理队列处理和状态更新
	go func() {
		// 创建一个等待组，用于等待所有 goroutine 完成
		var wg sync.WaitGroup

		// 启动队列处理 goroutine
		wg.Add(1)
		go func() {
			defer wg.Done()

			// 队列处理器每 1 分钟运行一次
			queueTicker := time.NewTicker(1 * time.Minute)
			defer queueTicker.Stop()

			// 立即处理一次队列
			s.processQueue(processorCtx)

			// 然后每 3 分钟处理一次
			for {
				select {
				case <-queueTicker.C:
					s.processQueue(processorCtx)
				case <-processorCtx.Done():
					log.Println("Queue processor stopped")
					return
				}
			}
		}()

		// 启动状态更新 goroutine
		wg.Add(1)
		go func() {
			defer wg.Done()

			// 状态更新器每 2 分钟运行一次
			statusTicker := time.NewTicker(2 * time.Minute)
			defer statusTicker.Stop()

			// 立即更新一次状态
			s.updateServiceStatus(processorCtx)

			// 然后每 2 分钟更新一次
			for {
				select {
				case <-statusTicker.C:
					s.updateServiceStatus(processorCtx)
				case <-processorCtx.Done():
					log.Println("Status updater stopped")
					return
				}
			}
		}()

		wg.Add(1)
		go func() {
			defer wg.Done()
			// 创建一个停止通道
			stopChan := make(chan struct{})
			// 启动一个独立的 goroutine 来处理停止信号
			go func() {
				<-processorCtx.Done()
				close(stopChan)
			}()
			// 启动资源检查器（会自动立即执行一次）
			s.startResourceUsageChecker(processorCtx, stopChan)

			log.Println("Application resoureuage checker stopped")
		}()

		// 等待上下文取消
		<-processorCtx.Done()
		log.Println("Queue processor context canceled, waiting for goroutines to complete")

		// 等待所有 goroutine 完成
		wg.Wait()

		// 更新状态标志
		s.queueProcessorMu.Lock()
		s.queueProcessorRunning = false
		s.queueProcessorCancel = nil
		s.queueProcessorMu.Unlock()

		log.Println("Queue processor stopped completely")
	}()
}

// StopQueueProcessor 停止队列处理器
func (s *Service) StopQueueProcessor() {
	s.queueProcessorMu.Lock()
	defer s.queueProcessorMu.Unlock()

	if !s.queueProcessorRunning {
		log.Println("Queue processor is not running, ignoring stop request")
		return
	}

	log.Println("Stopping queue processor...")

	// 调用取消函数来停止处理器
	if s.queueProcessorCancel != nil {
		s.queueProcessorCancel()
		// 状态更新会在 goroutine 退出时完成
	}
}

// IsQueueProcessorRunning 返回队列处理器是否正在运行
func (s *Service) IsQueueProcessorRunning() bool {
	s.queueProcessorMu.Lock()
	defer s.queueProcessorMu.Unlock()

	return s.queueProcessorRunning
}

// processQueue 处理队列中的部署记录
func (s *Service) processQueue(ctx context.Context) {
	log.Println("Processing deployment queue...")

	// 如果数据库连接不可用，直接返回
	if s.deployRecordRepo == nil || s.workerRepo == nil {
		log.Println("Database connection not available, skipping queue processing")
		return
	}

	// 1. 检查可用的worker数量，设定一次获取部署记录的数量
	availableWorkerCount, err := s.getAvailableWorkerCount(ctx)
	if err != nil {
		log.Printf("Failed to get available worker count: %v", err)
		return
	}

	// 如果可用的worker为0，就不必再获取部署记录了
	if availableWorkerCount == 0 {
		log.Println("No available workers found, skipping queue processing")
		return
	}

	// 根据可用worker数量设定批量大小
	batchSize := s.calculateBatchSize(availableWorkerCount)
	log.Printf("Found %d available workers, setting batch size to %d", availableWorkerCount, batchSize)

	// 2. 批量获取部署记录
	records, err := s.deployRecordRepo.GetNextQueueingRecord(batchSize)
	if err != nil {
		log.Printf("No queueing records found or error: %v", err)
		return
	}

	// 如果获取到的记录为0，结束，等待下一轮的轮询
	if len(records) == 0 {
		log.Println("No queueing records found, waiting for next polling cycle")
		return
	}

	log.Printf("Retrieved %d queueing records for processing", len(records))

	// 3. 逐条部署记录
	processedCount := 0
	skippedCount := 0
	errorCount := 0

	for i, record := range records {
		log.Printf("Processing record %d/%d: %s (%s)", i+1, len(records), record.ServiceID, record.Name)

		// 处理单条部署记录
		result := s.processSingleDeployRecord(ctx, record)

		switch result {
		case "SUCCESS":
			processedCount++
			log.Printf("✅ Record %s processed successfully", record.ServiceID)
			// 等上一条部署请求返回后，再执行下一个部署记录
			if i < len(records)-1 { // 不是最后一条记录
				log.Printf("Waiting for worker status update before processing next record...")
				time.Sleep(30 * time.Second) // 给Worker充足时间更新状态（异步部署需要更长时间）
			}

		case "NO_WORKER":
			skippedCount++
			log.Printf("⏭️  Record %s skipped (no available worker)", record.ServiceID)
			// 如果没有可用的worker，不必重试，继续下一条

		case "ERROR":
			errorCount++
			log.Printf("❌ Record %s processing failed", record.ServiceID)
			// 错误情况记录日志，不在此处更新状态
			// 状态更新应该在具体的处理逻辑完成时进行
		}
	}

	log.Printf("Queue processing completed: processed=%d, skipped=%d, errors=%d, total=%d",
		processedCount, skippedCount, errorCount, len(records))
}

// getAvailableWorkerCount 获取可用的worker数量
func (s *Service) getAvailableWorkerCount(ctx context.Context) (int, error) {
	// 获取所有AVAILABLE状态的worker
	workers, err := s.workerRepo.GetAll(&models.WorkerFilter{
		Status: "AVAILABLE",
	})
	if err != nil {
		return 0, fmt.Errorf("failed to get available workers: %w", err)
	}

	return len(workers), nil
}

// calculateBatchSize 根据可用worker数量计算批量大小
// 注意：调用此方法前已确保workerCount > 0
func (s *Service) calculateBatchSize(workerCount int) int {
	switch {
	case workerCount <= 3:
		return workerCount // 小规模集群，批量大小等于worker数
	case workerCount <= 10:
		return workerCount + 2 // 中等规模，稍微超配
	default:
		return min(workerCount, 20) // 大规模集群，限制最大批量为20
	}
}

// processSingleDeployRecord 处理单条部署记录
// 返回值: "SUCCESS", "NO_WORKER", "ERROR"
func (s *Service) processSingleDeployRecord(ctx context.Context, record models.DeployRecord) string {
	workers, err := s.selectAvailableWorker(&record)
	if err != nil {
		log.Printf("Failed to select available worker: %v", err)
		if strings.Contains(err.Error(), "no AVAILABLE workers") {
			return "NO_WORKER"
		}
		// 部署逻辑完成，更新状态为FAILED
		s.updateRecordStatusOnFailure(record.ServiceID, "Worker selection failed: "+err.Error())
		return "ERROR"
	}

	// 调用原有的部署逻辑，但将其封装为可以返回结果的形式
	success := s.executeDeployment(ctx, record, workers)
	if success {
		return "SUCCESS"
	}
	// 部署失败时，不更新状态为FAILED，而是跳过等待下一轮
	log.Printf("No suitable worker available for deployment %s, will retry in next cycle", record.ServiceID)
	return "NO_WORKER"
}

// updateRecordStatusOnFailure 在部署失败时更新记录状态
func (s *Service) updateRecordStatusOnFailure(serviceID, reason string) {
	if updateErr := s.deployRecordRepo.UpdateStatus(serviceID, "FAILED"); updateErr != nil {
		log.Printf("Failed to update record %s status to FAILED: %v", serviceID, updateErr)
	} else {
		log.Printf("Updated record %s status to FAILED due to: %s", serviceID, reason)
	}
}

// executeDeployment 执行部署逻辑（从原有代码提取）
func (s *Service) executeDeployment(ctx context.Context, record models.DeployRecord, workers []models.WorkerInfo) bool {
	// 获取部署记录中的资源需求
	// 处理 API 服务的资源需求
	apiReplica := record.ApiReplica
	if apiReplica <= 0 {
		apiReplica = 1
		log.Printf("No API replica specified, using default value: 1")
	}
	apiCPU := record.ApiCpu * float64(apiReplica) // API 服务总 CPU 需求
	apiMemory := record.ApiMemory * apiReplica    // API 服务总内存需求

	// 处理 Auto 服务的资源需求
	autoReplica := record.AutoReplica
	if autoReplica <= 0 {
		autoReplica = 0 // Auto 服务可以不部署
		log.Printf("No Auto replica specified, using default value: 0")
	}
	autoCPU := record.AutoCpu * float64(autoReplica) // Auto 服务总 CPU 需求
	autoMemory := record.AutoMemory * autoReplica    // Auto 服务总内存需求

	// 计算总资源需求
	totalCPU := apiCPU + autoCPU          // 总 CPU 需求
	totalMemory := apiMemory + autoMemory // 总内存需求

	log.Printf("API service requires: CPU=%.2f cores (%.2f x %d), Memory=%dMB (%d x %d)",
		apiCPU, record.ApiCpu, apiReplica, apiMemory, record.ApiMemory, apiReplica)
	log.Printf("Auto service requires: CPU=%.2f cores (%.2f x %d), Memory=%dMB (%d x %d)",
		autoCPU, record.AutoCpu, autoReplica, autoMemory, record.AutoMemory, autoReplica)
	log.Printf("Total deployment requires: CPU=%.2f cores, Memory=%dMB",
		totalCPU, totalMemory)

	// 用于存储所有满足条件的节点
	var candidates []NodeCandidate

	// 遍历所有可用的 worker
	for _, workerInfo := range workers {
		// ✅ 优化：直接使用selectAvailableWorker返回的完整worker信息，无需重新查询
		log.Printf("Checking worker: %s (%s) with %d nodes",
			workerInfo.Name, workerInfo.WorkerID, len(workerInfo.Nodes))

		// 遍历 worker 的所有节点
		for _, node := range workerInfo.Nodes {
			// 计算节点的可用资源
			// 注意：CPU 使用率和内存使用率是百分比值 (0-1)
			// 预留资源：CPU预留0.5核心，内存预留500MB
			const cpuReserved = 0.5
			const memoryReservedMB = 500

			// 设计理念：当前实际可用资源 - 预留资源 - 已分配资源限制（类似k8s limit）
			// 目的：避免多个服务同时达到峰值时资源不足，确保资源隔离
			availableCPU := node.CPU*(1-node.Cpu_use_rate) - cpuReserved - node.Cpu_allocation
			availableMemory := int(float64(node.Memory)*(1-node.Memory_use_rate)) - memoryReservedMB - node.Memory_allocation

			log.Printf("Node %s resource details:", node.IP)
			log.Printf("  Total CPU=%.2f cores, Usage=%.2f%%, Used=%.2f cores, Free=%.2f cores",
				node.CPU, node.Cpu_use_rate*100, node.CPU*node.Cpu_use_rate, node.CPU*(1-node.Cpu_use_rate))
			log.Printf("  CPU Reserved=%.2f cores, CPU Allocated=%.2f cores",
				cpuReserved, node.Cpu_allocation)
			log.Printf("  Available CPU = %.2f - %.2f - %.2f = %.2f cores",
				node.CPU*(1-node.Cpu_use_rate), cpuReserved, node.Cpu_allocation, availableCPU)

			log.Printf("  Total Memory=%dMB, Usage=%.2f%%, Used=%dMB, Free=%dMB",
				node.Memory, node.Memory_use_rate*100, int(float64(node.Memory)*node.Memory_use_rate), int(float64(node.Memory)*(1-node.Memory_use_rate)))
			log.Printf("  Memory Reserved=%dMB, Memory Allocated=%dMB",
				memoryReservedMB, node.Memory_allocation)
			log.Printf("  Available Memory = %d - %d - %d = %d MB",
				int(float64(node.Memory)*(1-node.Memory_use_rate)), memoryReservedMB, node.Memory_allocation, availableMemory)

			// 判断节点是否有足够的资源来满足部署需求
			if availableCPU >= totalCPU && availableMemory >= totalMemory {
				// 计算部署后的资源利用率
				// 资源利用率 = 已使用资源 / 总资源
				// 已使用资源 = 当前已使用 + 新部署需要的资源

				// 计算部署后的 CPU 利用率
				newCPUUsage := (node.CPU*node.Cpu_use_rate + totalCPU) / node.CPU

				// 计算部署后的内存利用率
				newMemoryUsage := (float64(node.Memory)*node.Memory_use_rate + float64(totalMemory)) / float64(node.Memory)

				// 计算综合利用率（CPU 和内存的平均值）
				newResourceUsage := (newCPUUsage + newMemoryUsage) / 2

				log.Printf("Node %s after deployment: CPU usage=%.2f%%, Memory usage=%.2f%%, Average usage=%.2f%%",
					node.IP, newCPUUsage*100, newMemoryUsage*100, newResourceUsage*100)

				// 将满足条件的节点添加到候选列表
				candidates = append(candidates, NodeCandidate{
					Worker:          workerInfo,
					NodeIP:          node.IP,
					ResourceUsage:   newResourceUsage,
					CPUUsage:        newCPUUsage,
					MemoryUsage:     newMemoryUsage,
					CPUAllocated:    totalCPU*node.Reserve_rate + node.Cpu_allocation,
					MemoryAllocated: int(float64(totalMemory)*node.Reserve_rate) + node.Memory_allocation,
					DiskAllocated:   0, // 暂时不考虑磁盘
				})

				log.Printf("Added node %s to candidates list with resource usage %.2f%%",
					node.IP, newResourceUsage*100)
			} else {
				log.Printf("Node %s does not have enough resources. Required: CPU=%.2f, Memory=%d; Available: CPU=%.2f, Memory=%d",
					node.IP, totalCPU, totalMemory, availableCPU, availableMemory)
			}
		}
	}

	// 如果没有找到合适的节点，返回失败
	if len(candidates) == 0 {
		log.Printf("No suitable node found for deployment %s", record.ServiceID)

		s.NotificationManager.SendNotificationWithDeduplication(context.Background(), &utils.NotificationMessage{
			Level:     utils.NotificationLevelWarning,
			Title:     "没有找到合适的节点",
			Content:   "没有找到合适的节点",
			ServiceID: record.ServiceID,
			Metadata:  nil,
			Timestamp: utils.GetCSTTimeString(),
		}, 12*time.Hour)
		return false
	}

	// 按照资源利用率排序（从低到高）
	sort.Slice(candidates, func(i, j int) bool {
		return candidates[i].ResourceUsage < candidates[j].ResourceUsage
	})

	// 输出所有候选节点的信息
	log.Printf("Found %d suitable nodes for deployment:", len(candidates))
	for i, candidate := range candidates {
		log.Printf("  %d. Worker=%s, Node=%s, Resource usage=%.2f%%, CPU usage=%.2f%%, Memory usage=%.2f%%",
			i+1, candidate.Worker.WorkerID, candidate.NodeIP,
			candidate.ResourceUsage*100, candidate.CPUUsage*100, candidate.MemoryUsage*100)
	}

	// 遍历候选节点，检查 Worker 的健康状态
	var selectedCandidate *NodeCandidate

	for i, candidate := range candidates {
		// 创建 Worker 客户端，调用健康检查接口
		workerClient := NewWorkerHTTPClient(candidate.Worker.Host)

		// 调用 Worker 的健康检查接口
		log.Printf("Checking real-time health status of worker %s (%s)",
			candidate.Worker.WorkerID, candidate.Worker.Host)

		// 设置健康检查超时，稍微延长以确保获取准确状态
		healthCtx, cancel := context.WithTimeout(ctx, 8*time.Second)
		healthResp, err := workerClient.HealthCheck(healthCtx)
		cancel() // 立即释放资源

		if err != nil {
			log.Printf("Failed to check health status of worker %s: %v",
				candidate.Worker.WorkerID, err)
			// 健康检查失败，可能worker不可用，跳过此候选节点
			continue
		}

		// 检查响应码
		if healthResp.Code != 200 {
			log.Printf("Worker %s health check returned error code %d: %s",
				candidate.Worker.WorkerID, healthResp.Code, healthResp.Msg)
			continue
		}

		// 检查 Worker 的状态
		statusData, ok := healthResp.Data.(map[string]interface{})
		if !ok {
			log.Printf("Invalid health response format from worker %s",
				candidate.Worker.WorkerID)
			continue // 如果响应格式不正确，尝试下一个候选节点
		}

		status, ok := statusData["status"].(string)
		if !ok {
			log.Printf("Invalid status format in health response from worker %s",
				candidate.Worker.WorkerID)
			continue // 如果状态格式不正确，尝试下一个候选节点
		}

		log.Printf("Worker %s real-time health status: %s", candidate.Worker.WorkerID, status)

		// 只有 Worker 状态为 AVAILABLE 时才选择该节点
		if status == "AVAILABLE" {
			selectedCandidate = &candidates[i]
			log.Printf("✅ Selected worker %s with confirmed AVAILABLE status",
				candidate.Worker.WorkerID)
			break
		} else {
			log.Printf("❌ Worker %s is in %s status, checking next candidate",
				candidate.Worker.WorkerID, status)
		}
	}

	// 如果没有找到可用的 Worker，跳过当前记录等待下一轮
	if selectedCandidate == nil {
		log.Printf("No worker with AVAILABLE status found for deployment %s, skipping for now",
			record.ServiceID)
		return false
	}

	// 执行实际的部署操作
	return s.performActualDeployment(ctx, record, selectedCandidate)
}

// performActualDeployment 执行实际的部署操作
func (s *Service) performActualDeployment(ctx context.Context, record models.DeployRecord, selectedCandidate *NodeCandidate) bool {
	log.Printf("Final node selection: Worker=%s, Node=%s, Resource usage after deployment=%.2f%%",
		selectedCandidate.Worker.WorkerID, selectedCandidate.NodeIP, selectedCandidate.ResourceUsage*100)

	// 获取选中的 worker 和节点
	selectedWorker := selectedCandidate.Worker
	selectedNodeIP := selectedCandidate.NodeIP
	selectedLabels := selectedCandidate.Worker.Labels

	// 更新部署记录的 worker ID 和节点 IP
	record.WorkerID = selectedWorker.WorkerID
	record.NodeIP = selectedNodeIP
	record.Status = "PROCESSING" // 更新状态为处理中
	record.HostIP = selectedWorker.HostIP

	// 获取 worker 的 domain_suffix
	if record.DomainSuffix == "" && selectedWorker.DomainSuffix != "" {
		record.DomainSuffix = selectedWorker.DomainSuffix
		log.Printf("Using domain suffix from worker: %s", record.DomainSuffix)
	}

	// 通过 record.ImageName 获取数据库中对应的 imageURL 和 ports 数据
	image, err := s.imageTypeRepo.GetByName(record.ImageName)
	if err != nil {
		log.Printf("Failed to get image information for image name %s: %v", record.ImageName, err)

		// 区分错误类型
		errorMsg := err.Error()
		if strings.Contains(errorMsg, "no rows in result set") ||
			strings.Contains(errorMsg, "not found") ||
			strings.Contains(errorMsg, "no such image") {

			// 镜像不存在 - 永久性错误
			log.Printf("Image '%s' does not exist in database, marking as ERROR", record.ImageName)

			record.Status = "ERROR"
			record.Remark = "镜像不存在"

			if updateErr := s.deployRecordRepo.Update(record); updateErr != nil {
				log.Printf("Failed to update record %s status to ERROR: %v", record.ServiceID, updateErr)
			} else {
				log.Printf("✅ Updated record %s status to ERROR due to image not found: %s",
					record.ServiceID, record.ImageName)
			}

			s.NotificationManager.SendNotificationWithDeduplication(context.Background(), &utils.NotificationMessage{
				Level:     utils.NotificationLevelError,
				Title:     "镜像不存在",
				Content:   "镜像不存在",
				ServiceID: record.ServiceID,
				Metadata:  nil,
				Timestamp: utils.GetCSTTimeString(),
			}, 120*time.Minute)

		} else {
			// 其他错误 - 可能是临时性错误
			log.Printf("Temporary error getting image info for %s, will retry later: %v",
				record.ImageName, err)
		}

		return false
	}

	// 记录获取到的镜像信息
	log.Printf("Found image for %s: URL=%s, Ports=%v", record.ImageName, image.ImageURL, image.Ports)

	// 创建 worker 客户端
	workerClient := NewWorkerHTTPClient(selectedWorker.Host)

	// 创建基础标签
	baseLabels := []string{
		"system=zero-ops-platform_app-deploy",
		"service-type=" + record.ServiceType,
	}

	// 将 image.Labels 聚合到标签中，并进行排重处理
	allLabels := utils.MergeLabelsWithDeduplication(baseLabels, selectedLabels, record.Labels, image.Labels)
	record.Labels = allLabels

	// 创建部署请求
	workerReq := &models.WorkerDeployRequest{
		ImageName:       record.ImageName, // 使用镜像类型作为镜像名称
		ImageURL:        image.ImageURL,
		DomainPrefix:    record.DomainPrefix, // 从 deploy_record 表获取的域名前缀
		DomainSuffix:    record.DomainSuffix, // 从 workers 表获取的域名后缀
		NodeIP:          selectedNodeIP,      // 使用选中的节点 IP
		HostIP:          record.HostIP,
		Expiration:      record.Expiration,      // 设置过期时间
		DurationSeconds: record.DurationSeconds, // 设置持续时间（秒）
		ApiCpu:          record.ApiCpu,          // 使用 API CPU 作为 CPU 核心数
		ApiMemory:       record.ApiMemory,       // 使用 API 内存作为内存大小
		ApiReplica:      record.ApiReplica,
		AutoCpu:         record.AutoCpu,
		AutoMemory:      record.AutoMemory,
		AutoReplica:     record.AutoReplica,
		ServiceId:       record.ServiceID,    // 使用服务 ID 作为容器名称
		CustomerEnvs:    record.CustomerEnvs, // 用户自定义环境变量
		Ports:           image.Ports,         // 从 image_type 表获取的容器端口列表
		Labels:          allLabels,           // 添加聚合后的标签
	}

	// 记录所有标签
	log.Printf("Using %d labels for deployment: %v", len(allLabels), allLabels)

	// 记录将要发送的域名和端口信息
	log.Printf("Sending domain information to worker: prefix=%s, suffix=%s",
		record.DomainPrefix, record.DomainSuffix)
	log.Printf("Sending ports information to worker: %v", image.Ports)

	// 部署前再次确认Worker状态（双重保险）
	log.Printf("Final confirmation: checking worker %s status before deployment", selectedWorker.WorkerID)
	finalHealthCtx, finalCancel := context.WithTimeout(ctx, 5*time.Second)
	finalHealthResp, err := workerClient.HealthCheck(finalHealthCtx)
	finalCancel()

	if err != nil {
		log.Printf("Final health check failed for worker %s: %v", selectedWorker.WorkerID, err)
		return false
	}

	if finalHealthResp.Code != 200 {
		log.Printf("Final health check returned error for worker %s: %s", selectedWorker.WorkerID, finalHealthResp.Msg)
		return false
	}

	if finalStatusData, ok := finalHealthResp.Data.(map[string]interface{}); ok {
		if finalStatus, ok := finalStatusData["status"].(string); ok && finalStatus != "AVAILABLE" {
			log.Printf("Worker %s status changed to %s before deployment, aborting", selectedWorker.WorkerID, finalStatus)
			return false
		}
	}

	// 调用 worker 的 DeployContainer 函数
	log.Printf("Sending deployment request to worker %s, node %s", selectedWorker.WorkerID, selectedNodeIP)

	// 设置部署请求超时（部署可能需要较长时间）
	deployCtx, cancel := context.WithTimeout(ctx, 60*time.Second)
	defer cancel()

	workerResp, err := workerClient.DeployContainer(deployCtx, workerReq)
	if err != nil {
		log.Printf("Failed to deploy service: %v", err)
		return false
	}

	// 检查响应
	if workerResp.Code != 200 {
		log.Printf("Worker returned error: %s", workerResp.Msg)
		// 检查是否是因为Worker状态问题导致的失败
		if strings.Contains(strings.ToLower(workerResp.Msg), "freeze") ||
			strings.Contains(strings.ToLower(workerResp.Msg), "busy") ||
			strings.Contains(strings.ToLower(workerResp.Msg), "unavailable") {
			log.Printf("Deployment failed due to worker status issue: %s", workerResp.Msg)
		}
		return false
	}

	// 更新service record（包含状态和其他信息）
	if err = s.deployRecordRepo.Update(record); err != nil {
		log.Printf("Failed to update deploy record with selected worker and node: %v", err)
		return false
	}

	// 更新 worker 资源使用情况到数据库
	log.Printf("Updating resource allocation for worker %s, node %s",
		selectedCandidate.Worker.WorkerID, selectedCandidate.NodeIP)

	// 🔑 关键修复：获取该worker的专用锁，避免并发资源更新冲突
	workerLock := s.getWorkerLock(selectedCandidate.Worker.WorkerID)
	workerLock.Lock()
	defer workerLock.Unlock()

	log.Printf("🔒 Acquired worker lock for %s, proceeding with deployment resource allocation update", selectedCandidate.Worker.WorkerID)

	// 🔑 重要：在锁保护下重新获取最新的worker信息，确保数据一致性
	// 避免使用可能过期的selectedCandidate.Worker数据
	currentWorker, err := s.workerRepo.GetByID(selectedCandidate.Worker.WorkerID)
	if err != nil {
		log.Printf("Failed to get fresh worker info for deployment resource allocation: %v", err)
		return false
	}
	log.Printf("🔄 Fetched fresh worker info from database for %s under lock protection", selectedCandidate.Worker.WorkerID)

	// 查找对应的节点并更新资源分配
	nodeUpdated := false
	for i := range currentWorker.Nodes {
		if currentWorker.Nodes[i].IP == selectedCandidate.NodeIP {
			// 重新计算资源分配值（基于最新的节点数据）
			// 获取服务的资源需求
			totalCPU := record.ApiCpu * float64(record.ApiReplica)
			totalMemory := float64(record.ApiMemory) * float64(record.ApiReplica)

			// 计算新的分配值（累加模式）
			newCPUAllocated := totalCPU*currentWorker.Nodes[i].Reserve_rate + currentWorker.Nodes[i].Cpu_allocation
			newMemoryAllocated := int(totalMemory*currentWorker.Nodes[i].Reserve_rate) + currentWorker.Nodes[i].Memory_allocation

			// 更新节点的资源分配数据
			log.Printf("Updating node %s resource allocation: CPU=%.2f→%.2f, Memory=%d→%d, Disk=%d→%d",
				selectedCandidate.NodeIP,
				currentWorker.Nodes[i].Cpu_allocation, newCPUAllocated,
				currentWorker.Nodes[i].Memory_allocation, newMemoryAllocated,
				currentWorker.Nodes[i].Disk_allocation, 0)

			currentWorker.Nodes[i].Cpu_allocation = newCPUAllocated
			currentWorker.Nodes[i].Memory_allocation = newMemoryAllocated
			// Disk_allocation 暂时不更新，保持原值
			nodeUpdated = true
			break
		}
	}

	if nodeUpdated {
		// 更新数据库中的 worker 信息
		fmt.Println(currentWorker, "================")
		if err := s.workerRepo.Update(currentWorker); err != nil {
			fmt.Printf("Failed to update worker resource allocation in database: %v", err)
		} else {
			fmt.Printf("Successfully updated resource allocation for worker %s, node %s",
				selectedCandidate.Worker.WorkerID, selectedCandidate.NodeIP)
		}
	} else {
		fmt.Printf("Warning: Node %s not found in worker %s for resource allocation update",
			selectedCandidate.NodeIP, selectedCandidate.Worker.WorkerID)
	}

	log.Printf("Successfully deployed service %s", record.ServiceID)
	return true
}

// NodeCandidate 定义节点候选者结构体（移到包级别以便复用）
type NodeCandidate struct {
	Worker          models.WorkerInfo // Worker 信息
	NodeIP          string            // 节点 IP
	ResourceUsage   float64           // 部署后的资源利用率
	CPUUsage        float64           // 部署后的 CPU 利用率
	MemoryUsage     float64           // 部署后的内存利用率
	CPUAllocated    float64           // 部署后节点已分配的 CPU 核心数
	MemoryAllocated int               // 部署后节点已分配的内存大小（MB）
	DiskAllocated   int               // 部署后节点已使用的磁盘空间（MB）
}

// selectAvailableWorker 选择可用的 worker 列表
func (s *Service) selectAvailableWorker(record *models.DeployRecord) ([]models.WorkerInfo, error) {
	if s.workerRepo == nil {
		return nil, fmt.Errorf("database connection not available")
	}

	// 如果 record.WorkerID 不为空，检查 workers 表里是否有这个 worker
	if record.WorkerID != "" {
		log.Printf("Record has WorkerID: %s, checking if worker exists", record.WorkerID)

		// 尝试从数据库获取指定的 worker
		worker, err := s.workerRepo.GetByID(record.WorkerID)
		if err == nil && worker.WorkerID == record.WorkerID {
			// worker 存在直接返回这个 worker
			log.Printf("Found existing worker %s for record %s", worker.WorkerID, record.ServiceID)
			return []models.WorkerInfo{worker}, nil
		}

		// worker 不存在 - 永久性错误，更新记录状态
		log.Printf("Worker %s not found, marking record as ERROR", record.WorkerID)

		// 更新记录状态为 ERROR，remark 为具体错误原因
		record.Status = "ERROR"
		record.Remark = fmt.Sprintf("指定Worker不存在: %s", record.WorkerID)

		if updateErr := s.deployRecordRepo.Update(*record); updateErr != nil {
			log.Printf("Failed to update record %s status to ERROR: %v", record.ServiceID, updateErr)
		} else {
			log.Printf("✅ Updated record %s status to ERROR due to worker not found: %s",
				record.ServiceID, record.WorkerID)
		}

		return nil, fmt.Errorf("worker %s not found or not AVAILABLE", record.WorkerID)
	}

	// 获取所有与部署记录服务类型匹配的 worker
	workers, err := s.workerRepo.GetAll(&models.WorkerFilter{
		ServerType: record.ServiceType,
		Status:     "AVAILABLE", // 只选择活跃状态的 worker
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get workers from database: %w", err)
	}

	// 检查是否有可用的 worker
	if len(workers) == 0 {
		return nil, fmt.Errorf("no AVAILABLE workers available for service type: %s", record.ServiceType)
	}

	log.Printf("Found %d AVAILABLE workers for service type: %s", len(workers), record.ServiceType)
	return workers, nil
}

// updateServiceStatus 更新服务状态
//  1. 从 deploy_record 表获取 status 字段值为 PROCESSING 或 RUNNING 两种状态的数据
//  2. 按数据中 worker_id 字段进行分组
//     a. 通过 worker_id 从 workers 表获取 host
//     b. 把同组的 service_id 用逗号拼接到一起
//  3. 调用 worker 层接口 worker.GET("/status", workerCtrl.GetDeployStatus) 获取相关数据
func (s *Service) updateServiceStatus(ctx context.Context) error {
	log.Println("Updating service status...")

	// 如果数据库连接不可用，直接返回
	if s.deployRecordRepo == nil || s.workerRepo == nil {
		log.Println("Database connection not available, skipping service status update")
		return fmt.Errorf("database connection not available")
	}

	// 1. 从 deploy_record 表获取 status 字段值为 PROCESSING 或 RUNNING 两种状态的数据
	records, err := s.deployRecordRepo.GetRecordsByStatus([]string{"PROCESSING", "RUNNING", "FAILED"})
	if err != nil {
		log.Printf("Failed to get deploy records: %v", err)
		return fmt.Errorf("failed to get deploy records: %w", err)
	}

	if len(records) == 0 {
		log.Println("No PROCESSING or RUNNING records found, skipping service status update")
		return nil
	}

	log.Printf("Found %d PROCESSING or RUNNING or FAILED records", len(records))

	// 2. 按数据中 worker_id 字段进行分组
	workerGroups := make(map[string][]models.DeployRecord)
	for _, record := range records {
		if record.WorkerID != "" {
			workerGroups[record.WorkerID] = append(workerGroups[record.WorkerID], record)
		} else if record.Status == "PROCESSING" && record.Remark == "DELETE_REQUESTED" {
			// 处理删除请求但没有WorkerID的异常情况
			log.Printf("Found orphaned DELETE_REQUESTED record without WorkerID: %s, updating to STOPPED", record.ServiceID)

			if err := s.deployRecordRepo.UpdateStatus(record.ServiceID, "STOPPED"); err != nil {
				log.Printf("Failed to update orphaned DELETE_REQUESTED record status: %v", err)
			} else {
				log.Printf("Successfully updated orphaned DELETE_REQUESTED record %s to STOPPED", record.ServiceID)

				// 发送通知
				s.NotificationManager.SendNotification(context.Background(), &utils.NotificationMessage{
					Level:     utils.NotificationLevelInfo,
					Title:     "无worker情况，删除请求记录",
					Content:   fmt.Sprintf("无worker情况，删除请求记录: %s", record.ServiceID),
					ServiceID: record.ServiceID,
					Metadata:  nil,
					Timestamp: utils.GetCSTTimeString(),
				})
			}
		}
	}

	if len(workerGroups) == 0 {
		log.Println("No records with valid worker_id found, skipping service status update")
		return nil
	}

	// 遍历每个 worker 组
	for workerID, workerRecords := range workerGroups {
		// a. 通过 worker_id 从 workers 表获取 host
		workerInfo, err := s.workerRepo.GetByID(workerID)
		if err != nil {
			log.Printf("Failed to get worker info for worker_id %s: %v", workerID, err)
			continue
		}

		if workerInfo.Host == "" {
			log.Printf("Worker %s has no host information, skipping", workerID)
			continue
		}

		// b. 把同组的 service_id 用逗号拼接到一起
		var serviceIDs []string
		for _, record := range workerRecords {
			serviceIDs = append(serviceIDs, record.ServiceID)
		}

		log.Printf("Querying status for %d services from worker %s (%s)", len(serviceIDs), workerID, workerInfo.Host)

		// 3. 调用 worker 层接口获取相关数据
		workerClient := NewWorkerHTTPClient(workerInfo.Host)
		resp, err := workerClient.GetDeployStatus(ctx, serviceIDs)
		if err != nil {
			log.Printf("Failed to get deploy status from worker %s: %v", workerID, err)
			continue
		}

		// 处理响应
		if resp.Code != 200 {
			log.Printf("Worker %s returned error: %s", workerID, resp.Msg)
			continue
		}

		// 解析响应数据
		data, ok := resp.Data.(map[string]interface{})
		if !ok {
			log.Printf("Invalid response format from worker %s", workerID)
			continue
		}

		// 统一处理记录，将单条记录转换为数组格式
		var recordsToProcess []map[string]interface{}

		// 检查是否是单条记录格式（直接包含 service_id 和 status）
		if serviceID, hasServiceID := data["service_id"].(string); hasServiceID {
			if status, hasStatus := data["status"].(string); hasStatus {
				// 将单条记录转换为数组格式
				recordsToProcess = []map[string]interface{}{
					{
						"service_id": serviceID,
						"status":     status,
					},
				}
			}
		} else if records, hasRecords := data["records"].([]interface{}); hasRecords {
			// 多条记录的情况，转换为统一格式
			for _, record := range records {
				if recordMap, isMap := record.(map[string]interface{}); isMap {
					recordsToProcess = append(recordsToProcess, recordMap)
				}
			}
		} else {
			log.Printf("Response from worker %s does not contain valid records", workerID)
			continue
		}

		// 处理所有记录
		for _, recordMap := range recordsToProcess {
			serviceID, ok := recordMap["service_id"].(string)
			if !ok {
				log.Printf("Invalid service_id format in record from worker %s", workerID)
				continue
			}

			status, ok := recordMap["status"].(string)
			if !ok {
				log.Printf("Invalid status format in record from worker %s", workerID)
				continue
			}

			// 提取 visited_at 字段（如果存在）
			var visitedAt string
			if visitedAtValue, hasVisitedAt := recordMap["visited_at"]; hasVisitedAt {
				if visitedAtStr, isString := visitedAtValue.(string); isString && visitedAtStr != "" {
					visitedAt = visitedAtStr
				}
			}

			// 这里添加判断，如果 status 的值为以下几种情况之一，才进行更新，否则跳过
			// RUNNING 运行中|TERMINATION 到期中止|STOPPED 人工停止|FAILED 部署异常
			validStatuses := map[string]bool{
				"RUNNING":     true,
				"TERMINATION": true,
				"STOPPED":     true,
				"FAILED":      true,
			}

			if !validStatuses[status] {
				log.Printf("Skipping update for service %s with invalid status: %s", serviceID, status)
				continue
			}

			// 更新数据库中的状态，同时同步 visited_at 字段
			if visitedAt != "" {
				// 如果有 visited_at 数据，获取完整记录并更新
				record, err := s.deployRecordRepo.GetByID(serviceID)
				if err != nil {
					log.Printf("Failed to get record for service %s: %v", serviceID, err)
					// 回退到只更新状态
					if err := s.deployRecordRepo.UpdateStatus(serviceID, status); err != nil {
						log.Printf("Failed to update status for service %s: %v", serviceID, err)
					}
				} else {
					// 更新状态和 visited_at
					record.Status = status
					record.VisitedAt = visitedAt
					if err := s.deployRecordRepo.Update(record); err != nil {
						log.Printf("Failed to update record for service %s: %v", serviceID, err)
					} else {
						log.Printf("Updated status for service %s to %s and synced visited_at: %s", serviceID, status, visitedAt)
					}
				}
			} else {
				// 没有 visited_at 数据，只更新状态
				if err := s.deployRecordRepo.UpdateStatus(serviceID, status); err != nil {
					log.Printf("Failed to update status for service %s: %v", serviceID, err)
				} else {
					log.Printf("Updated status for service %s to %s", serviceID, status)
				}
			}
			log.Printf("Service %s status changed to %s, resource allocation will be updated by periodic task", serviceID, status)

			// 如果status=="FAILED", 需要发送通知
			if status == "FAILED" {
				// 获取服务详细信息用于通知
				record, err := s.deployRecordRepo.GetByID(serviceID)
				if err != nil {
					log.Printf("Failed to get service record for notification: %v", err)
				} else {
					s.NotificationManager.SendNotificationWithDeduplication(context.Background(), &utils.NotificationMessage{
						Level:     utils.NotificationLevelError,
						Title:     "服务部署失败",
						Content:   fmt.Sprintf("服务部署失败, service_id: %s, worker_id: %s", serviceID, record.WorkerID),
						ServiceID: "system",
						Metadata:  nil,
						Timestamp: utils.GetCSTTimeString(),
					}, 120*time.Minute)
				}
			}
		}

		// 处理未找到的记录
		notFound, ok := data["not_found"].([]interface{})
		if ok && len(notFound) > 0 {
			for _, nfID := range notFound {
				serviceID, ok := nfID.(string)
				if !ok {
					continue
				}
				log.Printf("Service %s not found on worker %s", serviceID, workerID)

				// 将未找到的记录标记为 UNKNOWN 状态
				if err := s.deployRecordRepo.UpdateStatus(serviceID, "UNKNOWN"); err != nil {
					log.Printf("Failed to update status for not found service %s: %v", serviceID, err)
				}
				s.NotificationManager.SendNotification(context.Background(), &utils.NotificationMessage{
					Level:     utils.NotificationLevelSuccess,
					Title:     "数据不一致，出现游离record",
					Content:   "数据不一致，出现游离record",
					ServiceID: serviceID,
					Metadata:  nil,
					Timestamp: utils.GetCSTTimeString(),
				})
			}
		}
	}

	log.Println("Service status update completed")
	return nil
}

// ==================== 独立的资源检查逻辑 ====================

// 注意：checkResourceUsageUnified 方法已废弃
// 功能已分离为 checkHardwareResourceUsage 和 checkOSSResourceUsage 两个独立方法

// getRunningServices 获取运行中的服务列表（共享逻辑）
// 包含 RUNNING 和 PROCESSING 状态，确保资源分配计算的完整性
func (s *Service) getRunningServices(ctx context.Context) ([]models.DeployRecord, error) {
	if s.deployRecordRepo == nil {
		return nil, fmt.Errorf("数据库连接不可用")
	}

	// 修复：包含 PROCESSING 状态，避免部署成功但状态未及时更新导致的资源分配遗漏
	runningRecords, err := s.deployRecordRepo.GetRecordsByStatus([]string{"RUNNING", "PROCESSING"})
	if err != nil {
		return nil, fmt.Errorf("获取运行中的部署记录失败: %w", err)
	}

	return runningRecords, nil
}

func (s *Service) startResourceUsageChecker(ctx context.Context, stopChan <-chan struct{}) {
	log.Println("Starting resource usage checkers...")

	var wg sync.WaitGroup

	// 启动硬件资源检查器（始终启用，15分钟间隔）
	wg.Add(1)
	go func() {
		defer wg.Done()
		s.startHardwareResourceChecker(ctx, stopChan)
	}()

	// 启动OSS资源检查器（可选启用，2小时间隔）
	if utils.GetEnvBool("ENABLE_OSS_MONITORING", false) {
		wg.Add(1)
		go func() {
			defer wg.Done()
			s.startOSSResourceChecker(ctx, stopChan)
		}()
	} else {
		log.Println("OSS monitoring disabled, skipping OSS resource checker")
	}

	// 等待停止信号
	<-stopChan
	log.Println("Received stop signal, stopping all resource checkers")

	// 等待所有检查器停止
	wg.Wait()
	log.Println("All resource checkers stopped")
}

// startHardwareResourceChecker 启动硬件资源检查器
func (s *Service) startHardwareResourceChecker(ctx context.Context, stopChan <-chan struct{}) {
	log.Println("Starting hardware resource checker with 15 minutes interval")

	ticker := time.NewTicker(15 * time.Minute)
	defer ticker.Stop()

	// 立即执行一次
	log.Println("Performing initial hardware resource check")
	s.checkHardwareResourceUsage(ctx)

	// 定时检查循环
	for {
		select {
		case <-ticker.C:
			log.Println("Hardware resource checker triggered")
			s.checkHardwareResourceUsage(ctx)
		case <-stopChan:
			log.Println("Hardware resource checker stopped")
			return
		}
	}
}

// startOSSResourceChecker 启动OSS资源检查器
func (s *Service) startOSSResourceChecker(ctx context.Context, stopChan <-chan struct{}) {
	log.Println("Starting OSS resource checker with 2 hours interval")

	ticker := time.NewTicker(12 * time.Hour)
	defer ticker.Stop()

	// 立即执行一次
	log.Println("Performing initial OSS resource check")
	s.checkOSSResourceUsage(ctx)

	// 定时检查循环
	for {
		select {
		case <-ticker.C:
			log.Println("OSS resource checker triggered")
			s.checkOSSResourceUsage(ctx)
		case <-stopChan:
			log.Println("OSS resource checker stopped")
			return
		}
	}
}

// checkHardwareResourceUsage 检查服务器硬件资源使用情况
// 专注于CPU、内存、磁盘等硬件资源的分配统计
func (s *Service) checkHardwareResourceUsage(ctx context.Context) {
	log.Println("开始检查服务器硬件资源使用情况...")

	// 获取运行中的服务
	runningRecords, err := s.getRunningServices(ctx)
	if err != nil {
		log.Printf("获取运行中服务失败: %v", err)
		return
	}

	if len(runningRecords) == 0 {
		log.Println("没有运行中或处理中的服务，跳过硬件资源检查")
		return
	}

	log.Printf("找到 %d 个运行中或处理中的服务，开始更新硬件资源分配", len(runningRecords))

	// 更新硬件资源分配
	s.updateHardwareResourceAllocation(runningRecords)

	log.Printf("硬件资源检查完成，处理了 %d 个服务", len(runningRecords))
}

// checkOSSResourceUsage OSS资源使用检查
// 包含：OSS存储统计、OSS流量统计、日志预拉取
func (s *Service) checkOSSResourceUsage(ctx context.Context) {
	log.Println("开始OSS资源使用检查（2小时周期）...")

	// 获取运行中的服务
	runningRecords, err := s.getRunningServices(ctx)
	if err != nil {
		log.Printf("获取运行中服务失败: %v", err)
		return
	}

	if len(runningRecords) == 0 {
		log.Println("没有运行中的服务，跳过OSS资源检查")
		return
	}

	// 计算当前时间
	currentTime := time.Now()

	// 并行处理OSS相关任务
	var wg sync.WaitGroup

	// 任务1: OSS存储量和文件数统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		s.processOSSStorageStats(ctx, runningRecords)
	}()

	// 任务2: OSS流量统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		s.processOSSTrafficStats(ctx, runningRecords, currentTime)
	}()

	wg.Wait()

	log.Printf("OSS资源检查完成，处理了 %d 个服务", len(runningRecords))
}

// processOSSStorageStats 处理OSS存储量和文件数统计（基于 business_id 优化）
func (s *Service) processOSSStorageStats(ctx context.Context, records []models.DeployRecord) {
	log.Printf("开始处理 %d 个服务的OSS存储统计", len(records))

	if s.ossManager == nil {
		log.Printf("OSS管理器不可用，跳过所有服务的存储统计")
		return
	}

	// 提取唯一的 business_id，避免重复处理
	businessIDToRecord := make(map[string]models.DeployRecord)
	for _, record := range records {
		businessID, err := s.getBusinessIDFromServiceID(record.ServiceID)
		if err != nil {
			log.Printf("获取 businessID 失败 for service %s: %v", record.ServiceID, err)
			continue
		}

		// 如果同一个业务有多个服务，使用第一个遇到的服务记录作为代表
		if _, exists := businessIDToRecord[businessID]; !exists {
			businessIDToRecord[businessID] = record
		}
	}

	log.Printf("从 %d 个服务中提取到 %d 个唯一业务进行存储统计", len(records), len(businessIDToRecord))

	// 基于 business_id 进行存储统计
	for businessID, record := range businessIDToRecord {
		// 获取存储统计（包含存储量和文件数）
		ossResult, err := s.ossManager.GetServiceOSSStatus(ctx, record)
		if err != nil {
			log.Printf("获取业务 %s 的OSS存储统计失败: %v", businessID, err)
			continue
		}

		if !ossResult.Success {
			log.Printf("业务 %s 的OSS存储检查失败: %s", businessID, ossResult.ErrorMessage)
			continue
		}

		// 提取存储和文件数据
		var diskUsage int64
		var fileCount int64

		if ossResult.OSSUtilStats != nil {
			diskUsage = int64(ossResult.OSSUtilStats.TotalSizeMB)
			fileCount = ossResult.OSSUtilStats.ObjectCount
		}

		// 直接使用 businessID 更新存储数据
		if err := s.updateOSSStorageDataWithBusinessID(businessID, diskUsage, fileCount, ossResult); err != nil {
			log.Printf("更新业务 %s 的OSS存储数据失败: %v", businessID, err)
			continue
		}

		log.Printf("业务 %s OSS存储统计完成: 存储量=%dMB, 文件数=%d",
			businessID, diskUsage, fileCount)
	}

	log.Printf("OSS存储统计处理完成")
}

// updateOSSStorageDataWithBusinessID 直接基于 businessID 更新OSS存储相关数据
func (s *Service) updateOSSStorageDataWithBusinessID(businessID string, diskUsage int64, fileCount int64, ossResult *models.OSSStatusResult) error {
	// 获取现有记录
	existingUsage, err := s.resourceUsageRepo.GetByBusinessID(businessID)
	if err != nil {
		// 数据库查询错误，返回错误
		log.Printf("获取业务 %s 的现有资源使用记录失败: %v", businessID, err)
		return fmt.Errorf("获取业务资源使用记录失败: %w", err)
	}

	if existingUsage == nil {
		// 记录不存在，创建新记录
		log.Printf("业务 %s 的资源使用记录不存在，创建新记录", businessID)

		newUsage := &models.ServiceResourceUsage{
			BusinessID:   businessID,
			OSSDiskUsage: diskUsage,
		}

		// 创建元数据
		metadata := &models.ResourceUsageMetadata{
			OSSFileCount:   fileCount,
			CollectionTime: time.Now(),
			DataSources:    []string{"ossutil"},
		}

		// 添加OSS配置信息
		if ossResult.OSSConfig != nil {
			metadata.OSSBucket = ossResult.OSSConfig.Bucket
			metadata.OSSEndpoint = ossResult.OSSConfig.Endpoint
			metadata.OSSRegion = ossResult.OSSConfig.Region
			metadata.OSSFilePath = ossResult.OSSConfig.FilePath
		}

		// 设置元数据
		if err := newUsage.SetMetadata(metadata); err != nil {
			log.Printf("设置元数据失败: %v", err)
		}

		now := time.Now()
		newUsage.LastAccessTime = &now

		if err := s.resourceUsageRepo.Create(newUsage); err != nil {
			log.Printf("创建业务 %s 的资源使用记录失败: %v", businessID, err)
			return fmt.Errorf("创建资源使用记录失败: %w", err)
		}

		log.Printf("成功创建业务 %s 的资源使用记录: 文件数=%d, 存储量=%dMB", businessID, fileCount, diskUsage)
		return nil
	}

	// 记录存在，更新现有记录的存储数据
	log.Printf("业务 %s 的资源使用记录已存在，更新数据", businessID)

	existingUsage.OSSDiskUsage = diskUsage

	// 获取现有元数据
	metadata, err := existingUsage.GetMetadata()
	if err != nil {
		log.Printf("获取现有元数据失败，创建新的元数据: %v", err)
		metadata = &models.ResourceUsageMetadata{}
	}

	// 更新元数据
	metadata.OSSFileCount = fileCount
	metadata.CollectionTime = time.Now()
	if metadata.DataSources == nil {
		metadata.DataSources = []string{"ossutil"}
	}

	log.Printf("更新业务 %s 的OSS元数据: 文件数=%d, 存储量=%dMB", businessID, fileCount, diskUsage)

	// 更新OSS配置信息
	if ossResult.OSSConfig != nil {
		metadata.OSSBucket = ossResult.OSSConfig.Bucket
		metadata.OSSEndpoint = ossResult.OSSConfig.Endpoint
		metadata.OSSRegion = ossResult.OSSConfig.Region
		metadata.OSSFilePath = ossResult.OSSConfig.FilePath
	}

	// 设置更新后的元数据
	if err := existingUsage.SetMetadata(metadata); err != nil {
		log.Printf("更新元数据失败: %v", err)
	} else {
		log.Printf("成功设置业务 %s 的元数据，JSON长度: %d", businessID, len(existingUsage.Metadata))
	}

	now := time.Now()
	existingUsage.LastAccessTime = &now

	if err := s.resourceUsageRepo.UpdateByBusinessID(existingUsage.BusinessID, existingUsage); err != nil {
		log.Printf("更新业务 %s 的资源使用记录失败: %v", existingUsage.BusinessID, err)
		return fmt.Errorf("更新资源使用记录失败: %w", err)
	}

	log.Printf("成功更新业务 %s 的资源使用记录: 文件数=%d, 存储量=%dMB", businessID, fileCount, diskUsage)
	return nil
}

// processOSSTrafficStats 处理OSS流量统计 - 🚀 基于 business_id 优化版本
func (s *Service) processOSSTrafficStats(ctx context.Context, records []models.DeployRecord, t time.Time) {
	buckets := make(map[string]bool, 5)
	for _, record := range records {
		if s.ossManager == nil {
			log.Printf("OSS管理器不可用，跳过服务 %s", record.ServiceID)
			continue
		}
		// 1. 通过oss配置，读取oss日志目录中的日志文件清单
		//    a. t 参数是当前的时间，取 “年、月、日”，结合bucket 组合成过滤条件 --include=“*{bucket}year-month-day*”
		//    b. 拉取范围，前一天
		// 2. 读取 dbdata/oss_logs/文件夹文件，对比上一步拉取结果，做比对
		// 3. 下载dbdata/oss_logs/缺少的日志文件
		downloaded, skipped, err := s.processServiceOSSLogs(ctx, record, &buckets, t)
		if err != nil {
			log.Printf("处理业务OSS日志失败 (service: %s): %v", record.ServiceID, err)
			continue
		}

		log.Printf("业务日志文件处理完成: 下载 %d 个, 跳过 %d 个 (service: %s)",
			downloaded, skipped, record.ServiceID)

		log.Printf("业务流量统计完成 (service: %s)", record.ServiceID)
	}

	// 4. 通过文件统计流量，更新数据库
	// TODO: 需要考虑这部分功能，应该放到循环外
	if err := s.processServiceTrafficStats(ctx, records, t); err != nil {
		log.Printf("处理服务流量统计失败: %v", err)
	}

	// 🧹 清理超过7天的OSS日志文件
	if err := s.cleanupOldOSSLogs(7); err != nil {
		log.Printf("⚠️  清理OSS日志文件失败: %v", err)
	}

	log.Printf("OSS流量统计处理完成")
}

// processServiceOSSLogs 处理单个服务的OSS日志文件下载
// 实现注释中的三个步骤：1.获取远程文件列表 2.对比本地文件 3.下载缺失文件
// 返回值：(下载文件数, 跳过文件数, 错误)
func (s *Service) processServiceOSSLogs(ctx context.Context, record models.DeployRecord, bucketCache *map[string]bool, t time.Time) (int, int, error) {
	// 1. 解析OSS配置
	ossConfig, err := s.ossManager.parseOSSConfigFromRecord(record)
	if err != nil {
		return 0, 0, fmt.Errorf("解析OSS配置失败: %w", err)
	}

	if (*bucketCache)[ossConfig.GetBucketName()] {
		return 0, 1, nil
	} else {
		(*bucketCache)[ossConfig.GetBucketName()] = true
	}

	// 2. 定义要检查的日期范围：前一天
	currentDate := t.Truncate(24 * time.Hour)
	previousDate := currentDate.AddDate(0, 0, -1)
	datesToCheck := []time.Time{previousDate}

	var totalDownloaded, totalSkipped int

	// 3. 遍历每个日期，获取远程文件列表并下载缺失文件
	for _, date := range datesToCheck {
		log.Printf("检查服务 %s 在 %s 的日志文件", record.ServiceID, date.Format("2006-01-02"))

		// 步骤1: 通过oss配置，读取oss日志目录中的日志文件清单
		// a. t 参数是当前的时间，取 "年、月、日"，结合bucket 组合成过滤条件 --include="*{bucket}year-month-day*"
		// b. 拉取范围，当前天，前一天
		// 步骤2: 读取 dbdata/oss_logs/文件夹文件，对比上一步拉取结果，做比对
		// ListLogFiles 方法已经实现了这个逻辑，返回本地不存在的文件列表
		filesToDownload, err := s.ossManager.provider.ListLogFiles(ossConfig, date)
		if err != nil {
			log.Printf("获取服务 %s 在 %s 的日志文件列表失败: %v",
				record.ServiceID, date.Format("2006-01-02"), err)
			continue // 继续处理下一个日期
		}

		log.Printf("服务 %s 在 %s 需要下载 %d 个日志文件",
			record.ServiceID, date.Format("2006-01-02"), len(filesToDownload))

		// 步骤3: 下载dbdata/oss_logs/缺少的日志文件
		downloaded, skipped := s.downloadOSSLogFiles(ctx, ossConfig, filesToDownload, record.ServiceID, date)
		totalDownloaded += downloaded
		totalSkipped += skipped
	}

	return totalDownloaded, totalSkipped, nil
}

// downloadOSSLogFiles 下载OSS日志文件
// 返回值：(下载文件数, 跳过文件数)
func (s *Service) downloadOSSLogFiles(ctx context.Context, ossConfig interfaces.OSSConfig, filesToDownload []string, serviceID string, date time.Time) (int, int) {
	if len(filesToDownload) == 0 {
		log.Printf("服务 %s 在 %s 没有需要下载的文件", serviceID, date.Format("2006-01-02"))
		return 0, 0
	}

	var downloaded, skipped int

	for _, logFile := range filesToDownload {
		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			log.Printf("上下文被取消，停止下载服务 %s 的日志文件", serviceID)
			return downloaded, skipped
		default:
		}

		log.Printf("开始下载服务 %s 的日志文件: %s", serviceID, logFile)

		// 下载文件
		localPath, err := s.ossManager.provider.DownloadLogFile(ossConfig, logFile)
		if err != nil {
			log.Printf("下载服务 %s 的日志文件 %s 失败: %v", serviceID, logFile, err)
			skipped++
			continue
		}

		downloaded++
		log.Printf("成功下载服务 %s 的日志文件: %s -> %s", serviceID, logFile, localPath)
	}

	log.Printf("服务 %s 在 %s 的文件下载完成: 成功 %d 个, 跳过 %d 个",
		serviceID, date.Format("2006-01-02"), downloaded, skipped)

	return downloaded, skipped
}

// cleanupOldOSSLogs 清理超过指定天数的OSS日志文件
// 默认清理超过7天的日志文件，避免磁盘空间占用过多
func (s *Service) cleanupOldOSSLogs(maxDays int) error {
	if maxDays <= 0 {
		maxDays = 7 // 默认保留7天
	}

	logDir := "./dbdata/oss_logs"

	// 检查目录是否存在
	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		log.Printf("📁 OSS日志目录不存在，无需清理: %s", logDir)
		return nil
	}

	// 计算截止时间：当前时间 - maxDays 天
	cutoffTime := time.Now().AddDate(0, 0, -maxDays)
	log.Printf("🧹 开始清理OSS日志文件: 删除 %s 之前的文件", cutoffTime.Format("2006-01-02"))

	// 读取目录中的所有文件
	files, err := os.ReadDir(logDir)
	if err != nil {
		return fmt.Errorf("读取OSS日志目录失败: %w", err)
	}

	var deletedCount, skippedCount int
	var totalSize int64

	for _, file := range files {
		if file.IsDir() {
			continue
		}

		filePath := filepath.Join(logDir, file.Name())

		// 获取文件信息
		fileInfo, err := file.Info()
		if err != nil {
			log.Printf("⚠️  获取文件信息失败: %s, 错误: %v", file.Name(), err)
			skippedCount++
			continue
		}

		// 检查文件修改时间
		if fileInfo.ModTime().Before(cutoffTime) {
			// 文件超过保留期限，删除
			fileSize := fileInfo.Size()
			if err := os.Remove(filePath); err != nil {
				log.Printf("❌ 删除文件失败: %s, 错误: %v", file.Name(), err)
				skippedCount++
				continue
			}

			log.Printf("🗑️  删除过期日志文件: %s (大小: %.2f MB, 修改时间: %s)",
				file.Name(), float64(fileSize)/(1024*1024), fileInfo.ModTime().Format("2006-01-02 15:04:05"))
			deletedCount++
			totalSize += fileSize
		} else {
			// 文件在保留期限内，跳过
			log.Printf("📄 保留日志文件: %s (修改时间: %s)",
				file.Name(), fileInfo.ModTime().Format("2006-01-02 15:04:05"))
		}
	}

	log.Printf("🎉 OSS日志清理完成: 删除 %d 个文件 (%.2f MB), 跳过 %d 个文件",
		deletedCount, float64(totalSize)/(1024*1024), skippedCount)

	return nil
}

// updateHardwareResourceAllocation 更新硬件资源分配情况
// 遍历RUNNING状态的记录，按worker_id和node_ip分组计算资源使用量，更新到对应的worker节点中
func (s *Service) updateHardwareResourceAllocation(runningRecords []models.DeployRecord) {
	if len(runningRecords) == 0 {
		log.Printf("No RUNNING or PROCESSING services found, skipping hardware resource update")
		return
	}

	log.Printf("Found %d RUNNING or PROCESSING services for hardware resource calculation", len(runningRecords))

	// 统计各状态的服务数量，便于调试
	statusCount := make(map[string]int)
	for _, record := range runningRecords {
		statusCount[record.Status]++
	}
	log.Printf("Service status breakdown: %+v", statusCount)

	// 1. 按照 worker_id + node_ip 的组合对部署记录进行分组
	// 使用 map[string][]models.DeployRecord 进行分组，key为 "worker_id:node_ip"
	groupedRecords := make(map[string][]models.DeployRecord)

	for _, record := range runningRecords {
		// 跳过没有worker_id或node_ip的记录
		if record.WorkerID == "" || record.NodeIP == "" {
			log.Printf("Skipping service %s: missing WorkerID or NodeIP", record.ServiceID)
			continue
		}

		// 创建分组key
		groupKey := fmt.Sprintf("%s:%s", record.WorkerID, record.NodeIP)
		groupedRecords[groupKey] = append(groupedRecords[groupKey], record)
	}

	log.Printf("Grouped services into %d worker-node combinations", len(groupedRecords))

	// 2. 遍历每个分组，计算资源使用量并更新
	for groupKey, records := range groupedRecords {
		if err := s.updateNodeResourceAllocation(groupKey, records); err != nil {
			log.Printf("Failed to update resource allocation for %s: %v", groupKey, err)
		}
	}

	log.Printf("Completed hardware resource allocation update")
}

// updateNodeResourceAllocation 更新单个节点的资源分配
// 使用Worker级别的锁避免并发更新冲突
func (s *Service) updateNodeResourceAllocation(groupKey string, records []models.DeployRecord) error {
	// 解析 groupKey 获取 workerID 和 nodeIP
	parts := strings.Split(groupKey, ":")
	if len(parts) != 2 {
		return fmt.Errorf("invalid group key format: %s", groupKey)
	}

	workerID := parts[0]
	nodeIP := parts[1]

	log.Printf("Updating resource allocation for worker %s, node %s (%d services)",
		workerID, nodeIP, len(records))

	// 🔑 关键优化：获取该worker的专用锁，避免并发资源更新冲突
	workerLock := s.getWorkerLock(workerID)
	workerLock.Lock()
	defer workerLock.Unlock()

	log.Printf("🔒 Acquired worker lock for %s, proceeding with node resource allocation update", workerID)

	// 1. 计算该节点上所有RUNNING服务的总资源使用量
	var totalCPU float64
	var totalMemory float64

	for _, record := range records {
		// 计算单个服务的总资源使用量（单实例资源 × 实例数）
		serviceCPU := record.ApiCpu * float64(record.ApiReplica)
		serviceMemory := float64(record.ApiMemory) * float64(record.ApiReplica)

		totalCPU += serviceCPU
		totalMemory += serviceMemory

		log.Printf("Service %s (Status: %s): CPU=%.2f cores (%.2f * %d), Memory=%.0f MB (%d * %d)",
			record.ServiceID, record.Status, serviceCPU, record.ApiCpu, record.ApiReplica,
			serviceMemory, record.ApiMemory, record.ApiReplica)
	}

	log.Printf("Total resource usage on node %s: CPU=%.2f cores, Memory=%.0f MB",
		nodeIP, totalCPU, totalMemory)

	// 2. 获取worker信息
	// 🔑 重要：在锁保护下获取最新的worker信息，确保数据一致性
	worker, err := s.workerRepo.GetByID(workerID)
	if err != nil {
		return fmt.Errorf("failed to get fresh worker info for %s: %w", workerID, err)
	}
	log.Printf("🔄 Fetched fresh worker info from database for %s under lock protection", workerID)

	// 3. 查找对应的节点
	nodeFound := false
	for i := range worker.Nodes {
		if worker.Nodes[i].IP == nodeIP {
			nodeFound = true

			// 获取节点的 Reserve_rate
			reserveRate := worker.Nodes[i].Reserve_rate

			// 4. 计算应该被记录的使用量（总使用量 × Reserve_rate）
			allocatedCPU := totalCPU * reserveRate
			allocatedMemory := int(totalMemory * reserveRate)

			log.Printf("Node %s reserve_rate=%.2f, calculated allocation: CPU=%.2f cores, Memory=%d MB",
				nodeIP, reserveRate, allocatedCPU, allocatedMemory)

			// 记录更新前的状态
			log.Printf("Before update - Node %s: CPU_allocation=%.2f, Memory_allocation=%d",
				nodeIP, worker.Nodes[i].Cpu_allocation, worker.Nodes[i].Memory_allocation)

			// 5. 更新节点的资源分配
			worker.Nodes[i].Cpu_allocation = allocatedCPU
			worker.Nodes[i].Memory_allocation = allocatedMemory

			// 记录更新后的状态
			log.Printf("After update - Node %s: CPU_allocation=%.2f, Memory_allocation=%d",
				nodeIP, worker.Nodes[i].Cpu_allocation, worker.Nodes[i].Memory_allocation)

			break
		}
	}

	if !nodeFound {
		return fmt.Errorf("node %s not found in worker %s", nodeIP, workerID)
	}

	// 6. 更新数据库中的worker信息
	if err := s.workerRepo.Update(worker); err != nil {
		return fmt.Errorf("failed to update worker resource allocation in database: %w", err)
	}

	log.Printf("Successfully updated resource allocation for worker %s, node %s", workerID, nodeIP)
	return nil
}

// cleanupWorkerLocks 清理不再使用的worker锁，避免内存泄漏
// 建议定期调用此方法，比如在worker监控中
func (s *Service) cleanupWorkerLocks() {
	s.workerLocksMu.Lock()
	defer s.workerLocksMu.Unlock()

	if s.workerLocks == nil {
		return
	}

	// 获取当前活跃的worker列表
	activeWorkers, err := s.workerRepo.GetAll(nil)
	if err != nil {
		log.Printf("Failed to get active workers for lock cleanup: %v", err)
		return
	}

	// 创建活跃worker ID的集合
	activeWorkerIDs := make(map[string]bool)
	for _, worker := range activeWorkers {
		activeWorkerIDs[worker.WorkerID] = true
	}

	// 清理不再活跃的worker锁
	cleanedCount := 0
	for workerID := range s.workerLocks {
		if !activeWorkerIDs[workerID] {
			delete(s.workerLocks, workerID)
			cleanedCount++
		}
	}

	if cleanedCount > 0 {
		log.Printf("🧹 Cleaned up %d unused worker locks", cleanedCount)
	}
}

// processServiceTrafficStats 处理批量服务的流量统计 - 🚀 性能优化版本
// 一次性计算所有服务的流量统计，避免重复解析日志文件
func (s *Service) processServiceTrafficStats(ctx context.Context, records []models.DeployRecord, t time.Time) error {
	if s.ossManager == nil {
		return fmt.Errorf("OSS管理器不可用，跳过所有服务的流量统计")
	}

	if len(records) == 0 {
		log.Printf("没有需要处理的服务记录")
		return nil
	}

	// 🚀 增量优化：批量查询并过滤出需要处理的服务，避免重复处理
	// 1. 提取所有服务ID
	serviceIDs := make([]string, len(records))
	for i, record := range records {
		serviceIDs[i] = record.ServiceID
	}

	// 2. 提取所有需要的 businessIDs 并去重，然后批量查询 ServiceResourceUsage 数据
	businessIDSet := make(map[string]bool) // 用于去重

	for _, serviceID := range serviceIDs {
		businessID, err := s.getBusinessIDFromServiceID(serviceID)
		if err != nil {
			log.Printf("获取 businessID 失败 for service %s: %v，跳过该服务", serviceID, err)
			continue
		}
		businessIDSet[businessID] = true // 自动去重
	}

	// 将去重后的 businessID 转换为数组
	businessIDs := make([]string, 0, len(businessIDSet))
	for businessID := range businessIDSet {
		businessIDs = append(businessIDs, businessID)
	}

	log.Printf("从 %d 个服务中提取到 %d 个唯一业务ID", len(serviceIDs), len(businessIDs))

	// 批量查询已存在的业务资源使用记录
	businessResourceUsageMap, err := s.resourceUsageRepo.GetByBusinessIDs(businessIDs)
	if err != nil {
		log.Printf("批量查询业务资源使用记录失败: %v，按全部需要处理", err)
		// 如果查询失败，按全部需要处理
		businessResourceUsageMap = make(map[string]*models.ServiceResourceUsage)
	}

	// 3. 识别新的 business_id 并批量创建资源记录
	newBusinessIDs := make([]string, 0)
	for _, businessID := range businessIDs {
		if _, exists := businessResourceUsageMap[businessID]; !exists {
			newBusinessIDs = append(newBusinessIDs, businessID)
		}
	}

	// 为新业务批量创建资源使用记录
	if len(newBusinessIDs) > 0 {
		log.Printf("发现 %d 个新业务，创建资源使用记录: %v", len(newBusinessIDs), newBusinessIDs)

		for _, businessID := range newBusinessIDs {
			newUsage := &models.ServiceResourceUsage{
				BusinessID: businessID,
				// 其他字段使用默认值
			}

			if err := s.resourceUsageRepo.Create(newUsage); err != nil {
				log.Printf("创建业务 %s 的资源记录失败: %v", businessID, err)
				continue
			}

			// 添加到处理映射中，确保后续能找到
			businessResourceUsageMap[businessID] = newUsage
			log.Printf("成功创建业务 %s 的资源使用记录", businessID)
		}
	}

	// 4. 基于 business_id 创建增强版记录并过滤出需要处理的业务
	// 避免重复处理同一个 business_id
	var recordsToProcess []DeployRecordWithTrafficInfo
	processedBusinessIDs := make(map[string]bool)

	for _, record := range records {
		// 获取 businessID
		businessID, err := s.getBusinessIDFromServiceID(record.ServiceID)
		if err != nil {
			continue // 静默跳过错误
		}

		// 如果该业务已经处理过，跳过
		if processedBusinessIDs[businessID] {
			continue
		}

		// 获取业务资源记录（肯定存在）
		usage := businessResourceUsageMap[businessID]

		// 创建增强版记录（代表整个业务）
		enhancedRecord := DeployRecordWithTrafficInfo{
			DeployRecord:            record, // 使用第一个遇到的服务记录作为代表
			NetworkTrafficUpdatedAt: usage.NetworkTrafficUpdatedAt,
			CurrentOSSTraffic:       usage.OSSNetworkTraffic,
		}

		// 判断是否需要处理（基于业务级别判断）
		if s.shouldSkipBusinessWithUsage(businessID, usage, t) {
			processedBusinessIDs[businessID] = true // 标记为已处理
			continue
		}

		recordsToProcess = append(recordsToProcess, enhancedRecord)
		processedBusinessIDs[businessID] = true // 标记为已处理
	}

	if len(recordsToProcess) == 0 {
		log.Printf("📋 所有 %d 个服务都已处理过，无需重复处理", len(records))
		return nil
	}

	log.Printf("📋 需要处理 %d/%d 个服务的流量统计", len(recordsToProcess), len(records))

	// 🚀 核心优化：一次性计算需要处理的服务的流量统计
	allServicesTraffic, err := s.calculateAllServicesTrafficForDate(ctx, recordsToProcess, t)
	if err != nil {
		return fmt.Errorf("批量计算流量统计失败: %w", err)
	}

	log.Printf("批量流量计算完成，共统计 %d 个服务的流量", len(allServicesTraffic))

	// 批量更新数据库（只处理需要处理的服务）
	successCount := 0
	var lastError error

	for _, record := range recordsToProcess {
		// 🟡 中优先级：上下文检查
		select {
		case <-ctx.Done():
			return fmt.Errorf("上下文被取消，已处理 %d/%d 个服务", successCount, len(recordsToProcess))
		default:
		}

		if trafficMB, exists := allServicesTraffic[record.ServiceID]; exists {
			if err := s.updateOSSTrafficData(record.ServiceID, trafficMB); err != nil {
				// 🟡 中优先级：统一错误处理策略
				lastError = err
				log.Printf("更新服务 %s 的流量数据失败: %v", record.ServiceID, err)
				continue
			}
			// 🟡 中优先级：优化日志输出格式
			log.Printf("✅ 服务 %s 流量统计完成: +%.2f MB", record.ServiceID, trafficMB)
			successCount++
		} else {
			log.Printf("⚠️  服务 %s 没有找到流量数据", record.ServiceID)
		}
	}

	// 🟡 中优先级：统一的批量处理结果日志
	skippedCount := len(records) - len(recordsToProcess)
	if successCount == len(recordsToProcess) {
		log.Printf("🎉 OSS流量统计处理完成: 成功更新 %d 个服务, 跳过 %d 个服务", successCount, skippedCount)
	} else {
		log.Printf("⚠️  OSS流量统计部分完成: 成功更新 %d/%d 个服务, 跳过 %d 个服务", successCount, len(recordsToProcess), skippedCount)
		if lastError != nil {
			return fmt.Errorf("部分服务更新失败，最后错误: %w", lastError)
		}
	}

	return nil
}

// updateOSSTrafficData 更新OSS流量数据到数据库
func (s *Service) updateOSSTrafficData(serviceID string, trafficMB float64) error {
	// 从 serviceID 获取 businessID
	businessID, err := s.getBusinessIDFromServiceID(serviceID)
	if err != nil {
		log.Printf("获取 businessID 失败 for service %s: %v", serviceID, err)
		return fmt.Errorf("获取 businessID 失败: %w", err)
	}

	// 获取现有记录
	existingUsage, err := s.resourceUsageRepo.GetByBusinessID(businessID)
	if err != nil {
		log.Printf("获取业务 %s 的现有资源使用记录失败: %v", businessID, err)
		return fmt.Errorf("获取业务资源使用记录失败: %w", err)
	}

	currentTime := time.Now()

	if existingUsage == nil {
		// 记录不存在，创建新记录
		log.Printf("服务 %s 的资源使用记录不存在，创建新记录", serviceID)

		// 从 serviceID 获取 businessID
		businessID, err := s.getBusinessIDFromServiceID(serviceID)
		if err != nil {
			log.Printf("获取 businessID 失败 for service %s: %v", serviceID, err)
			return fmt.Errorf("获取 businessID 失败: %w", err)
		}

		newUsage := &models.ServiceResourceUsage{
			BusinessID:              businessID,
			OSSNetworkTraffic:       trafficMB,
			NetworkTrafficUpdatedAt: &currentTime,
		}

		// 创建元数据
		metadata := &models.ResourceUsageMetadata{
			CollectionTime: currentTime,
			DataSources:    []string{"oss_logs"},
		}

		// 设置元数据
		if err := newUsage.SetMetadata(metadata); err != nil {
			log.Printf("设置元数据失败: %v", err)
		}

		// 创建新记录
		if err := s.resourceUsageRepo.Create(newUsage); err != nil {
			return fmt.Errorf("创建资源使用记录失败: %w", err)
		}

		log.Printf("成功创建服务 %s 的资源使用记录: OSS流量=%.2fMB", serviceID, trafficMB)
	} else {
		// 记录存在，累加流量数据（增量计算）
		oldTraffic := existingUsage.OSSNetworkTraffic
		newTraffic := oldTraffic + trafficMB
		log.Printf("累加服务 %s 的OSS流量数据: 原值=%.2fMB, 增量=+%.2fMB, 新值=%.2fMB",
			serviceID, oldTraffic, trafficMB, newTraffic)

		// 🚀 关键：累加流量数据而不是覆盖
		existingUsage.OSSNetworkTraffic = newTraffic
		existingUsage.NetworkTrafficUpdatedAt = &currentTime

		// 更新元数据
		metadata, err := existingUsage.GetMetadata()
		if err != nil {
			log.Printf("获取元数据失败: %v", err)
			metadata = &models.ResourceUsageMetadata{}
		}

		// 更新数据源和收集时间
		metadata.CollectionTime = currentTime
		if metadata.DataSources == nil {
			metadata.DataSources = []string{"oss_logs"}
		} else {
			// 检查是否已包含 oss_logs
			found := false
			for _, source := range metadata.DataSources {
				if source == "oss_logs" {
					found = true
					break
				}
			}
			if !found {
				metadata.DataSources = append(metadata.DataSources, "oss_logs")
			}
		}

		// 设置更新后的元数据
		if err := existingUsage.SetMetadata(metadata); err != nil {
			log.Printf("设置元数据失败: %v", err)
		}

		// 更新记录
		if err := s.resourceUsageRepo.UpdateByBusinessID(existingUsage.BusinessID, existingUsage); err != nil {
			return fmt.Errorf("更新资源使用记录失败: %w", err)
		}

		log.Printf("成功更新服务 %s 的OSS流量数据: %.2fMB", serviceID, trafficMB)
	}

	return nil
}

// updateOSSTrafficDataWithPreviousDay 更新OSS流量数据到数据库，同时支持前一天流量记录
func (s *Service) updateOSSTrafficDataWithPreviousDay(serviceID string, totalTrafficMB, previousDayTrafficMB float64) error {
	// 从 serviceID 获取 businessID
	businessID, err := s.getBusinessIDFromServiceID(serviceID)
	if err != nil {
		log.Printf("获取 businessID 失败 for service %s: %v", serviceID, err)
		return fmt.Errorf("获取 businessID 失败: %w", err)
	}

	// 获取现有记录
	existingUsage, err := s.resourceUsageRepo.GetByBusinessID(businessID)
	if err != nil {
		log.Printf("获取业务 %s 的现有资源使用记录失败: %v", businessID, err)
		return fmt.Errorf("获取业务资源使用记录失败: %w", err)
	}

	currentTime := time.Now()

	if existingUsage == nil {
		// 记录不存在，创建新记录
		log.Printf("服务 %s 的资源使用记录不存在，创建新记录", serviceID)

		newUsage := &models.ServiceResourceUsage{
			BusinessID:              businessID,
			OSSNetworkTraffic:       totalTrafficMB,
			OSSNetworkTrafficByDay:  previousDayTrafficMB,
			NetworkTrafficUpdatedAt: &currentTime,
		}

		// 创建元数据
		metadata := &models.ResourceUsageMetadata{
			CollectionTime: currentTime,
			DataSources:    []string{"oss_logs"},
		}

		// 设置元数据
		if err := newUsage.SetMetadata(metadata); err != nil {
			log.Printf("设置元数据失败: %v", err)
		}

		// 创建新记录
		if err := s.resourceUsageRepo.Create(newUsage); err != nil {
			return fmt.Errorf("创建资源使用记录失败: %w", err)
		}

		log.Printf("成功创建服务 %s 的资源使用记录: 总流量=%.2fMB, 前一天=%.2fMB",
			serviceID, totalTrafficMB, previousDayTrafficMB)
	} else {
		// 记录存在，累加流量数据（增量计算）
		oldTraffic := existingUsage.OSSNetworkTraffic
		newTraffic := oldTraffic + totalTrafficMB
		log.Printf("累加服务 %s 的OSS流量数据: 原值=%.2fMB, 增量=+%.2fMB, 新值=%.2fMB",
			serviceID, oldTraffic, totalTrafficMB, newTraffic)

		// 🚀 关键：累加流量数据而不是覆盖，同时设置前一天流量
		existingUsage.OSSNetworkTraffic = newTraffic
		existingUsage.OSSNetworkTrafficByDay = previousDayTrafficMB
		existingUsage.NetworkTrafficUpdatedAt = &currentTime

		// 更新元数据
		metadata, err := existingUsage.GetMetadata()
		if err != nil {
			log.Printf("获取元数据失败: %v", err)
			metadata = &models.ResourceUsageMetadata{}
		}

		// 更新数据源和收集时间
		metadata.CollectionTime = currentTime
		if metadata.DataSources == nil {
			metadata.DataSources = []string{"oss_logs"}
		} else {
			// 检查是否已包含 oss_logs
			found := false
			for _, source := range metadata.DataSources {
				if source == "oss_logs" {
					found = true
					break
				}
			}
			if !found {
				metadata.DataSources = append(metadata.DataSources, "oss_logs")
			}
		}

		// 设置更新后的元数据
		if err := existingUsage.SetMetadata(metadata); err != nil {
			log.Printf("设置元数据失败: %v", err)
		}

		// 更新记录
		if err := s.resourceUsageRepo.UpdateByBusinessID(existingUsage.BusinessID, existingUsage); err != nil {
			return fmt.Errorf("更新资源使用记录失败: %w", err)
		}

		log.Printf("成功更新服务 %s 的OSS流量数据: 总流量增量=%.2fMB, 前一天=%.2fMB",
			serviceID, totalTrafficMB, previousDayTrafficMB)
	}

	return nil
}

// updateOSSTrafficDataWithBusinessID 直接基于 businessID 更新OSS流量数据到数据库
func (s *Service) updateOSSTrafficDataWithBusinessID(businessID string, totalTrafficMB, previousDayTrafficMB float64) error {
	// 获取现有记录
	existingUsage, err := s.resourceUsageRepo.GetByBusinessID(businessID)
	if err != nil {
		log.Printf("获取业务 %s 的现有资源使用记录失败: %v", businessID, err)
		return fmt.Errorf("获取业务资源使用记录失败: %w", err)
	}

	currentTime := time.Now()

	if existingUsage == nil {
		// 记录不存在，创建新记录
		log.Printf("业务 %s 的资源使用记录不存在，创建新记录", businessID)

		newUsage := &models.ServiceResourceUsage{
			BusinessID:              businessID,
			OSSNetworkTraffic:       totalTrafficMB,
			OSSNetworkTrafficByDay:  previousDayTrafficMB,
			NetworkTrafficUpdatedAt: &currentTime,
		}

		// 创建元数据
		metadata := &models.ResourceUsageMetadata{
			CollectionTime: currentTime,
			DataSources:    []string{"oss_logs"},
		}

		// 设置元数据
		if err := newUsage.SetMetadata(metadata); err != nil {
			log.Printf("设置元数据失败: %v", err)
		}

		// 创建新记录
		if err := s.resourceUsageRepo.Create(newUsage); err != nil {
			return fmt.Errorf("创建资源使用记录失败: %w", err)
		}

		log.Printf("成功创建业务 %s 的资源使用记录: 总流量=%.2fMB, 前一天=%.2fMB",
			businessID, totalTrafficMB, previousDayTrafficMB)
	} else {
		// 记录存在，累加流量数据（增量计算）
		oldTraffic := existingUsage.OSSNetworkTraffic
		newTraffic := oldTraffic + totalTrafficMB
		log.Printf("累加业务 %s 的OSS流量数据: 原值=%.2fMB, 增量=+%.2fMB, 新值=%.2fMB",
			businessID, oldTraffic, totalTrafficMB, newTraffic)

		// 🚀 关键：累加流量数据而不是覆盖，同时设置前一天流量
		existingUsage.OSSNetworkTraffic = newTraffic
		existingUsage.OSSNetworkTrafficByDay = previousDayTrafficMB
		existingUsage.NetworkTrafficUpdatedAt = &currentTime

		// 更新元数据
		metadata, err := existingUsage.GetMetadata()
		if err != nil {
			log.Printf("获取元数据失败: %v", err)
			metadata = &models.ResourceUsageMetadata{}
		}

		// 更新数据源和收集时间
		metadata.CollectionTime = currentTime
		if metadata.DataSources == nil {
			metadata.DataSources = []string{"oss_logs"}
		} else {
			// 检查是否已包含 oss_logs
			found := false
			for _, source := range metadata.DataSources {
				if source == "oss_logs" {
					found = true
					break
				}
			}
			if !found {
				metadata.DataSources = append(metadata.DataSources, "oss_logs")
			}
		}

		// 设置更新后的元数据
		if err := existingUsage.SetMetadata(metadata); err != nil {
			log.Printf("设置元数据失败: %v", err)
		}

		// 更新记录
		if err := s.resourceUsageRepo.UpdateByBusinessID(existingUsage.BusinessID, existingUsage); err != nil {
			return fmt.Errorf("更新资源使用记录失败: %w", err)
		}

		log.Printf("成功更新业务 %s 的OSS流量数据: 总流量增量=%.2fMB, 前一天=%.2fMB",
			businessID, totalTrafficMB, previousDayTrafficMB)
	}

	return nil
}

// calculateAllServicesTrafficForDate 🚀 多天回溯优化：支持处理多天历史日志的流量统计
// 核心改进：按 NetworkTrafficUpdatedAt 分组，从早到晚依次处理，支持服务中断后的完整数据恢复
// 使用增强版记录，可以访问流量统计相关的额外信息
func (s *Service) calculateAllServicesTrafficForDate(ctx context.Context, records []DeployRecordWithTrafficInfo, t time.Time) (map[string]float64, error) {
	// 0. 缓存当前处理时间，避免长时间处理过程中时间变化
	processingDate := t.Truncate(24 * time.Hour)
	log.Printf("🕒 缓存处理时间: %s，避免长时间任务中时间变化", processingDate.Format("2006-01-02"))

	// 🚀 新增：预处理 NetworkTrafficUpdatedAt 为空的记录
	processedRecords := s.preprocessRecordsWithNullTimestamp(records, t)

	// 1. 基于 NetworkTrafficUpdatedAt 字段进行分组
	groups := s.groupRecordsByStartDate(processedRecords, processingDate)
	if len(groups) == 0 {
		log.Printf("📋 没有需要处理的分组")
		return make(map[string]float64), nil
	}

	log.Printf("📊 创建了 %d 个处理分组", len(groups))

	// 2. 从较早的时间组开始处理
	finalResults, err := s.processGroupsSequentially(ctx, groups, processingDate)
	if err != nil {
		return nil, fmt.Errorf("分组处理失败: %w", err)
	}

	log.Printf("🎉 多天回溯处理完成: 共处理 %d 个服务的流量数据", len(finalResults))
	return finalResults, nil
}

// findAllLogFilesForDate 查找指定日期的所有日志文件
func (s *Service) findAllLogFilesForDate(dateStr string) ([]string, error) {
	logDir := "./dbdata/oss_logs"

	// 检查目录是否存在
	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		return nil, fmt.Errorf("日志目录不存在: %s", logDir)
	}

	// 读取目录中的所有文件
	files, err := os.ReadDir(logDir)
	if err != nil {
		return nil, fmt.Errorf("读取日志目录失败: %w", err)
	}

	var matchingFiles []string
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		fileName := file.Name()
		// 检查文件名是否包含指定日期（格式：YYYY-MM-DD）
		if strings.Contains(fileName, dateStr) {
			fullPath := filepath.Join(logDir, fileName)
			matchingFiles = append(matchingFiles, fullPath)
		}
	}

	return matchingFiles, nil
}

// isServiceTrafficMatch 判断文件路径是否属于指定服务的流量
// 🚀 优化：专门用于批量处理中的服务匹配逻辑
func (s *Service) isServiceTrafficMatch(filePath, serviceID string) bool {
	// 检查文件路径是否包含服务ID
	// 可能的格式：
	// 1. /service_id/...
	// 2. /path/service_id/...
	// 3. 文件名包含service_id

	// 简单的包含检查
	if strings.Contains(filePath, serviceID) {
		return true
	}

	// 检查路径段
	pathSegments := strings.Split(strings.Trim(filePath, "/"), "/")
	for _, segment := range pathSegments {
		if segment == serviceID {
			return true
		}
	}

	return false
}

// isBusinessTrafficMatch 判断文件路径是否属于指定业务的流量
// 🚀 优化：直接基于 business_id 的匹配逻辑（OSS路径已基于 business_id）
func (s *Service) isBusinessTrafficMatch(filePath, businessID string) bool {
	// 检查文件路径是否包含业务ID
	// 可能的格式：
	// 1. /business_id/...
	// 2. /path/business_id/...
	// 3. 文件名包含business_id

	// 简单的包含检查
	if strings.Contains(filePath, businessID) {
		return true
	}

	// 检查路径段
	pathSegments := strings.Split(strings.Trim(filePath, "/"), "/")
	for _, segment := range pathSegments {
		if segment == businessID {
			return true
		}
	}

	return false
}

// shouldSkipBusinessWithUsage 判断是否应该跳过业务的流量统计（基于 business_id 优化版本）
// 基于已获取的 ServiceResourceUsage 记录和新的时间关系定义判断是否需要处理
func (s *Service) shouldSkipBusinessWithUsage(businessID string, existingUsage *models.ServiceResourceUsage, currentTime time.Time) bool {
	// 首次处理，不跳过
	if existingUsage == nil || existingUsage.NetworkTrafficUpdatedAt == nil {
		log.Printf("📝 业务 %s: 首次处理，不跳过", businessID)
		return false
	}

	// 提取日期（东8区，直接处理）
	lastUpdatedDate := existingUsage.NetworkTrafficUpdatedAt.Truncate(24 * time.Hour)
	currentDate := currentTime.Truncate(24 * time.Hour)

	// 新的判断逻辑：
	// NetworkTrafficUpdatedAt == currentTime: 所有文件都处理完了，跳过
	// NetworkTrafficUpdatedAt < currentTime: 需要处理 NetworkTrafficUpdatedAt 时间的日志，不跳过
	// NetworkTrafficUpdatedAt > currentTime: 不可能情况（时钟回拨）

	if lastUpdatedDate.Equal(currentDate) {
		// 情况a: 所有文件都处理完了
		log.Printf("⏭️  跳过业务 %s: 所有日志都已处理完 (last_updated: %s == current: %s)",
			businessID, lastUpdatedDate.Format("2006-01-02"), currentDate.Format("2006-01-02"))
		return true
	} else if lastUpdatedDate.Before(currentDate) {
		// 情况b: 需要处理 NetworkTrafficUpdatedAt 时间的日志
		log.Printf("📝 业务 %s: 需要处理 %s 的日志 (last_updated: %s < current: %s)",
			businessID, lastUpdatedDate.Format("2006-01-02"),
			lastUpdatedDate.Format("2006-01-02"), currentDate.Format("2006-01-02"))
		return false
	} else {
		// 情况c: 时钟回拨异常
		log.Printf("⚠️  业务 %s: 异常情况 - 时钟回拨? (last_updated: %s > current: %s)",
			businessID, lastUpdatedDate.Format("2006-01-02"), currentDate.Format("2006-01-02"))
		return false // 保守处理，不跳过
	}
}

// shouldSkipServiceWithUsage 判断是否应该跳过服务的流量统计（保留用于其他地方的兼容性）
// 基于已获取的 ServiceResourceUsage 记录和新的时间关系定义判断是否需要处理
func (s *Service) shouldSkipServiceWithUsage(serviceID string, existingUsage *models.ServiceResourceUsage, currentTime time.Time) bool {
	// 首次处理，不跳过
	if existingUsage == nil || existingUsage.NetworkTrafficUpdatedAt == nil {
		log.Printf("📝 服务 %s: 首次处理，不跳过", serviceID)
		return false
	}

	// 提取日期（东8区，直接处理）
	lastUpdatedDate := existingUsage.NetworkTrafficUpdatedAt.Truncate(24 * time.Hour)
	currentDate := currentTime.Truncate(24 * time.Hour)

	// 新的判断逻辑：
	// NetworkTrafficUpdatedAt == currentTime: 所有文件都处理完了，跳过
	// NetworkTrafficUpdatedAt < currentTime: 需要处理 NetworkTrafficUpdatedAt 时间的日志，不跳过
	// NetworkTrafficUpdatedAt > currentTime: 不可能情况（时钟回拨）

	if lastUpdatedDate.Equal(currentDate) {
		// 情况a: 所有文件都处理完了
		log.Printf("⏭️  跳过服务 %s: 所有日志都已处理完 (last_updated: %s == current: %s)",
			serviceID, lastUpdatedDate.Format("2006-01-02"), currentDate.Format("2006-01-02"))
		return true
	} else if lastUpdatedDate.Before(currentDate) {
		// 情况b: 需要处理 NetworkTrafficUpdatedAt 时间的日志
		log.Printf("📝 服务 %s: 需要处理 %s 的日志 (last_updated: %s < current: %s)",
			serviceID, lastUpdatedDate.Format("2006-01-02"),
			lastUpdatedDate.Format("2006-01-02"), currentDate.Format("2006-01-02"))
		return false
	} else {
		// 情况c: 时钟回拨异常
		log.Printf("⚠️  服务 %s: 异常情况 - 时钟回拨? (last_updated: %s > current: %s)",
			serviceID, lastUpdatedDate.Format("2006-01-02"), currentDate.Format("2006-01-02"))
		return false // 保守处理，不跳过
	}
}

// preprocessRecordsWithNullTimestamp 预处理 NetworkTrafficUpdatedAt 为空的记录
// 将空值设置为当前时间 -7天，避免复杂的分组逻辑问题
func (s *Service) preprocessRecordsWithNullTimestamp(records []DeployRecordWithTrafficInfo, currentTime time.Time) []DeployRecordWithTrafficInfo {
	processedRecords := make([]DeployRecordWithTrafficInfo, len(records))

	// 计算默认时间：当前时间 -7天
	defaultTime := currentTime.AddDate(0, 0, -7)
	log.Printf("🔧 预处理：为空的 NetworkTrafficUpdatedAt 设置默认时间 %s", defaultTime.Format("2006-01-02 15:04:05"))

	nullCount := 0
	for i, record := range records {
		processedRecords[i] = record // 复制记录

		if record.NetworkTrafficUpdatedAt == nil {
			// 设置为当前时间 -7天
			processedRecords[i].NetworkTrafficUpdatedAt = &defaultTime
			log.Printf("📝 服务 %s: NetworkTrafficUpdatedAt 为空，设置为 %s",
				record.ServiceID, defaultTime.Format("2006-01-02 15:04:05"))
			nullCount++
		} else {
			log.Printf("📝 服务 %s: NetworkTrafficUpdatedAt = %s (无需修改)",
				record.ServiceID, record.NetworkTrafficUpdatedAt.Format("2006-01-02 15:04:05"))
		}
	}

	log.Printf("✅ 预处理完成: 处理了 %d 条记录，其中 %d 条设置了默认时间", len(processedRecords), nullCount)
	return processedRecords
}

// groupRecordsByStartDate 基于 NetworkTrafficUpdatedAt 字段对记录进行分组
// 返回按开始处理日期分组的记录集合
// 注意：调用此方法前，所有记录的 NetworkTrafficUpdatedAt 都应该已经被预处理过
func (s *Service) groupRecordsByStartDate(records []DeployRecordWithTrafficInfo, processingDate time.Time) map[string]*ProcessingGroup {
	groups := make(map[string]*ProcessingGroup)

	for _, record := range records {
		// 经过预处理，所有记录都应该有 NetworkTrafficUpdatedAt 值
		if record.NetworkTrafficUpdatedAt == nil {
			log.Printf("⚠️  警告：服务 %s 的 NetworkTrafficUpdatedAt 仍为空，跳过处理", record.ServiceID)
			continue
		}

		// 从上次更新的当天开始处理
		// NetworkTrafficUpdatedAt 表示已处理完前一天的日志，需要处理当天的日志
		lastUpdatedDate := record.NetworkTrafficUpdatedAt.Truncate(24 * time.Hour)
		startDate := lastUpdatedDate // 不需要+1天，直接处理当天的日志
		log.Printf("📝 服务 %s: 从 %s 开始处理 (上次更新: %s)",
			record.ServiceID, startDate.Format("2006-01-02"), lastUpdatedDate.Format("2006-01-02"))

		// 确保开始日期不超过处理日期的前一天
		targetDate := processingDate.AddDate(0, 0, -1)
		if startDate.After(targetDate) {
			log.Printf("⏭️  服务 %s: 开始日期 %s 超出处理范围 %s，跳过",
				record.ServiceID, startDate.Format("2006-01-02"), targetDate.Format("2006-01-02"))
			continue
		}

		groupKey := startDate.Format("2006-01-02")
		if groups[groupKey] == nil {
			groups[groupKey] = &ProcessingGroup{
				StartDate: startDate,
				Records:   []DeployRecordWithTrafficInfo{},
			}
			log.Printf("📁 创建新分组: %s", groupKey)
		}
		groups[groupKey].Records = append(groups[groupKey].Records, record)
	}

	// 输出分组统计
	for groupKey, group := range groups {
		log.Printf("📊 分组 %s: %d 个服务", groupKey, len(group.Records))
	}

	return groups
}

// processGroupsSequentially 按时间顺序处理分组，支持分组间的记录迁移
// 从较早的时间组开始处理，实现多天日志的完整回溯
func (s *Service) processGroupsSequentially(ctx context.Context, groups map[string]*ProcessingGroup, processingDate time.Time) (map[string]float64, error) {
	// 按日期排序分组键
	var sortedKeys []string
	for key := range groups {
		sortedKeys = append(sortedKeys, key)
	}
	sort.Strings(sortedKeys) // 从早到晚排序

	log.Printf("🔄 开始按时间顺序处理 %d 个分组: %v", len(sortedKeys), sortedKeys)

	// 全局服务流量累加器
	globalAccumulator := &ServiceTrafficAccumulator{
		totalTraffic:       make(map[string]float64),
		previousDayTraffic: make(map[string]float64),
	}

	// 按顺序处理每个分组
	for _, groupKey := range sortedKeys {
		group := groups[groupKey]
		if len(group.Records) == 0 {
			log.Printf("📋 分组 %s 为空，跳过", groupKey)
			continue
		}

		log.Printf("🚀 开始处理分组 %s: %d 个服务", groupKey, len(group.Records))

		// 处理该分组
		if err := s.processGroup(ctx, group, processingDate, groups, globalAccumulator); err != nil {
			return nil, fmt.Errorf("处理分组 %s 失败: %w", groupKey, err)
		}

		log.Printf("✅ 分组 %s 处理完成", groupKey)
	}

	// 🚀 关键改进：全部分组处理完后，统一更新数据库
	if err := s.batchUpdateDatabase(globalAccumulator, processingDate); err != nil {
		return nil, fmt.Errorf("批量更新数据库失败: %w", err)
	}

	// 返回最终结果
	globalAccumulator.mu.RLock()
	result := make(map[string]float64)
	for serviceID, traffic := range globalAccumulator.totalTraffic {
		result[serviceID] = traffic
	}
	globalAccumulator.mu.RUnlock()

	return result, nil
}

// processGroup 处理单个分组，支持多天日志处理和分组间迁移
// 这是核心的分组处理逻辑，实现了我们讨论的时间判断和分组迁移机制
func (s *Service) processGroup(ctx context.Context, group *ProcessingGroup, processingDate time.Time, allGroups map[string]*ProcessingGroup, globalAccumulator *ServiceTrafficAccumulator) error {
	currentLogDate := group.StartDate
	targetDate := processingDate.AddDate(0, 0, -1) // 目标：处理到前一天

	log.Printf("📅 分组处理范围: %s 到 %s", currentLogDate.Format("2006-01-02"), targetDate.Format("2006-01-02"))

	// 处理该分组需要的所有日期
	for !currentLogDate.After(targetDate) {
		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			return fmt.Errorf("上下文被取消")
		default:
		}

		dateStr := currentLogDate.Format("2006-01-02")
		log.Printf("📄 处理日期: %s 的日志", dateStr)

		// 获取该日期的所有日志文件
		logFiles, err := s.findAllLogFilesForDate(dateStr)
		if err != nil {
			log.Printf("⚠️  查找 %s 的日志文件失败: %v", dateStr, err)
			currentLogDate = currentLogDate.AddDate(0, 0, 1)
			continue
		}

		if len(logFiles) == 0 {
			log.Printf("📭 %s 没有找到日志文件", dateStr)
			currentLogDate = currentLogDate.AddDate(0, 0, 1)
			continue
		}

		log.Printf("📂 %s 找到 %d 个日志文件", dateStr, len(logFiles))

		// 为该组所有服务计算当天的流量
		dayTraffic, err := s.calculateDayTrafficForGroup(group.Records, logFiles)
		if err != nil {
			log.Printf("⚠️  计算 %s 流量失败: %v", dateStr, err)
			currentLogDate = currentLogDate.AddDate(0, 0, 1)
			continue
		}

		// 累加到全局累加器
		// 计算前一天日期
		previousDate := processingDate.AddDate(0, 0, -1).Format("2006-01-02")
		currentDateStr := currentLogDate.Format("2006-01-02")
		s.accumulateTraffic(globalAccumulator, currentDateStr, previousDate, dayTraffic)

		// 判断下一步处理逻辑
		nextLogDate := currentLogDate.AddDate(0, 0, 1)
		if nextLogDate.Before(processingDate) {
			// 情况1: 下个日期 < 当前处理日期，需要继续处理，移动到下个分组
			log.Printf("🔄 需要继续处理，移动到下个分组 (下个日期: %s < 处理日期: %s)",
				nextLogDate.Format("2006-01-02"), processingDate.Format("2006-01-02"))
			s.moveRecordsToNextGroup(group.Records, nextLogDate, allGroups, processingDate)
		} else if nextLogDate.Equal(processingDate) {
			// 情况2: 下个日期 == 当前处理日期，处理完成
			log.Printf("✅ 处理完成 (下个日期: %s == 处理日期: %s)",
				nextLogDate.Format("2006-01-02"), processingDate.Format("2006-01-02"))
			// 记录完成状态（实际更新在批量更新中进行）
			s.updateDatabaseForRecords(group.Records, globalAccumulator, currentLogDate)
		} else {
			// 情况3: 下个日期 > 当前处理日期，这种情况不应该出现
			log.Printf("⚠️  异常情况: 下个日期超出处理范围 (下个日期: %s > 处理日期: %s)",
				nextLogDate.Format("2006-01-02"), processingDate.Format("2006-01-02"))
		}

		currentLogDate = nextLogDate
	}

	return nil
}

// calculateDayTrafficForGroup 计算指定分组在指定日期的流量
// 每个日志文件只解析一次，为分组中所有业务计算流量（直接基于 business_id）
func (s *Service) calculateDayTrafficForGroup(records []DeployRecordWithTrafficInfo, logFiles []string) (map[string]float64, error) {
	dayTraffic := make(map[string]float64) // businessID -> traffic

	// 提取所有唯一的 business_id（避免重复处理）
	businessIDs := make(map[string]bool)
	for _, record := range records {
		businessID, err := s.getBusinessIDFromServiceID(record.ServiceID)
		if err != nil {
			log.Printf("⚠️  获取 businessID 失败 for service %s: %v", record.ServiceID, err)
			continue
		}
		businessIDs[businessID] = true
	}

	log.Printf("📊 需要统计 %d 个唯一业务的流量", len(businessIDs))

	// 遍历所有日志文件
	for _, logFile := range logFiles {
		// 解析日志文件（每个文件只解析一次）
		entries, err := s.ossManager.provider.ParseLogFile(logFile)
		if err != nil {
			log.Printf("⚠️  解析日志文件 %s 失败: %v", filepath.Base(logFile), err)
			continue
		}

		log.Printf("📄 文件 %s: 解析了 %d 条日志记录", filepath.Base(logFile), len(entries))

		// 直接基于 businessID 统计流量
		fileBusinessTraffic := make(map[string]int64)
		for businessID := range businessIDs {
			// 在当前日志文件的entries中查找该业务的流量
			for _, entry := range entries {
				if s.isBusinessTrafficMatch(entry.FilePath, businessID) {
					fileBusinessTraffic[businessID] += entry.ObjectSize
				}
			}
		}

		// 转换为MB并累加到日流量
		for businessID, bytes := range fileBusinessTraffic {
			trafficMB := float64(bytes) / (1024 * 1024)
			dayTraffic[businessID] += trafficMB
			if trafficMB > 0 {
				log.Printf("  ✅ 业务 %s: +%.2f MB", businessID, trafficMB)
			}
		}
	}

	return dayTraffic, nil
}

// accumulateTraffic 将日流量累加到全局累加器，支持前一天流量记录（基于 business_id）
func (s *Service) accumulateTraffic(accumulator *ServiceTrafficAccumulator, currentDate, previousDate string, dayTraffic map[string]float64) {
	accumulator.mu.Lock()
	defer accumulator.mu.Unlock()

	// 确保 map 已初始化
	if accumulator.totalTraffic == nil {
		accumulator.totalTraffic = make(map[string]float64)
	}
	if accumulator.previousDayTraffic == nil {
		accumulator.previousDayTraffic = make(map[string]float64)
	}

	// dayTraffic 现在是 businessID -> traffic
	for businessID, traffic := range dayTraffic {
		// 累加总流量
		accumulator.totalTraffic[businessID] += traffic

		// 如果当前处理的是前一天的数据，记录到 previousDayTraffic
		if currentDate == previousDate {
			accumulator.previousDayTraffic[businessID] = traffic
		}

		log.Printf("📊 累加业务 %s: +%.2f MB (总计: %.2f MB)",
			businessID, traffic, accumulator.totalTraffic[businessID])
	}
}

// moveRecordsToNextGroup 将记录移动到下一个分组
// 实现分组间的记录迁移，支持多天日志回溯
func (s *Service) moveRecordsToNextGroup(records []DeployRecordWithTrafficInfo, nextLogDate time.Time, allGroups map[string]*ProcessingGroup, processingDate time.Time) {
	// 🔑 关键判断：只有在需要继续处理时才创建下个分组
	if nextLogDate.Before(processingDate) {
		nextGroupKey := nextLogDate.Format("2006-01-02")

		// 如果下个分组不存在，创建它
		if allGroups[nextGroupKey] == nil {
			allGroups[nextGroupKey] = &ProcessingGroup{
				StartDate: nextLogDate,
				Records:   []DeployRecordWithTrafficInfo{},
			}
			log.Printf("📝 创建新分组: %s (条件: %s < %s)",
				nextGroupKey,
				nextLogDate.Format("2006-01-02"),
				processingDate.Format("2006-01-02"))
		}

		// 将记录移动到下个分组
		allGroups[nextGroupKey].Records = append(allGroups[nextGroupKey].Records, records...)
		log.Printf("🔄 移动 %d 条记录到分组 %s", len(records), nextGroupKey)

	} else {
		// 已经处理到最后一天，不需要创建新分组
		log.Printf("✅ 记录处理完成，无需创建新分组 (下个日期: %s >= 处理日期: %s)",
			nextLogDate.Format("2006-01-02"),
			processingDate.Format("2006-01-02"))
	}
}

// updateDatabaseForRecords 更新记录对应的数据库
// 注意：按照方案，这个方法现在只用于记录日志，实际更新在批量更新中进行
func (s *Service) updateDatabaseForRecords(records []DeployRecordWithTrafficInfo, accumulator *ServiceTrafficAccumulator, processedDate time.Time) {
	log.Printf("📋 分组处理完成，等待批量更新数据库 (处理日期: %s, 服务数: %d)",
		processedDate.Format("2006-01-02"), len(records))

	// 显示当前累加器状态（用于调试）
	accumulator.mu.RLock()
	for _, record := range records {
		if traffic, exists := accumulator.totalTraffic[record.ServiceID]; exists {
			log.Printf("  📊 服务 %s: 当前累计流量 %.2f MB", record.ServiceID, traffic)
		}
	}
	accumulator.mu.RUnlock()
}

// batchUpdateDatabase 批量更新数据库（基于 business_id）
// 按照方案：全部分组处理完后，统一更新数据库
func (s *Service) batchUpdateDatabase(accumulator *ServiceTrafficAccumulator, processingDate time.Time) error {
	accumulator.mu.RLock()
	defer accumulator.mu.RUnlock()

	if len(accumulator.totalTraffic) == 0 {
		log.Printf("📋 没有需要更新的业务流量数据")
		return nil
	}

	log.Printf("💾 开始批量更新数据库: %d 个业务", len(accumulator.totalTraffic))

	successCount := 0
	var lastError error

	// accumulator.totalTraffic 现在是 businessID -> traffic
	for businessID, totalTraffic := range accumulator.totalTraffic {
		if totalTraffic > 0 {
			// 获取前一天流量
			previousDayTraffic := accumulator.previousDayTraffic[businessID]

			// 直接使用 businessID 更新，无需转换
			if err := s.updateOSSTrafficDataWithBusinessID(businessID, totalTraffic, previousDayTraffic); err != nil {
				log.Printf("❌ 更新业务 %s 失败: %v", businessID, err)
				lastError = err
				continue
			}
			log.Printf("✅ 业务 %s: +%.2f MB, 前一天: %.2f MB (NetworkTrafficUpdatedAt 更新为 %s)",
				businessID, totalTraffic, previousDayTraffic, processingDate.Format("2006-01-02"))
			successCount++
		} else {
			log.Printf("⚠️  业务 %s: 流量为0，跳过更新", businessID)
		}
	}

	if successCount == len(accumulator.totalTraffic) {
		log.Printf("🎉 批量更新完成: 成功更新 %d/%d 个业务", successCount, len(accumulator.totalTraffic))
	} else {
		log.Printf("⚠️  批量更新部分完成: 成功更新 %d/%d 个业务", successCount, len(accumulator.totalTraffic))
		if lastError != nil {
			return fmt.Errorf("部分业务更新失败，最后错误: %w", lastError)
		}
	}

	return nil
}
