package service

import (
	"time"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/interfaces"
)

// OSSConfig OSS 配置接口（使用通用接口）
type OSSConfig = interfaces.OSSConfig

// OSSProvider OSS 提供商接口（基于工具，不使用 API）
type OSSProvider interface {
	// 连接测试（使用工具）
	TestConnection(config OSSConfig) error

	// 存储统计（仅使用工具）
	GetBucketStatsWithUtil(config OSSConfig) (*models.OSSUtilStats, error)

	// 日志处理（使用工具）
	DownloadLogFile(config OSSConfig, logPath string) (string, error)
	ListLogFiles(config OSSConfig, date time.Time) ([]string, error)
	ParseLogFile(filePath string) ([]*models.OSSLogEntry, error)
}

// OSSProviderType OSS 提供商类型
type OSSProviderType string

const (
	OSSProviderAliyun  OSSProviderType = "aliyun"
	OSSProviderTencent OSSProviderType = "tencent"
	OSSProviderHuawei  OSSProviderType = "huawei"
)
