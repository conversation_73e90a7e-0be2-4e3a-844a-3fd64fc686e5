package service

import (
	"context"
	"log"
	"time"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// NodesResourceResult 表示 worker 资源结果
type NodesResourceResult = []models.Node

// StartWorkerMonitor 启动 worker 监控器，每120s执行一次
func (s *Service) StartWorkerMonitor(ctx context.Context) {
	log.Println("Starting worker monitor...")

	// 启动一个独立的协程来监控 worker
	go func() {
		ticker := time.NewTicker(120 * time.Second)
		defer ticker.Stop()

		// 立即执行一次监控
		s.monitorWorkers(ctx)

		// 然后每120s执行一次
		for {
			select {
			case <-ticker.C:
				s.monitorWorkers(ctx)
			case <-ctx.Done():
				log.Println("Worker monitor stopped due to context cancellation")
				return
			}
		}
	}()
}

// monitorWorkers 监控所有 worker 的资源情况
func (s *Service) monitorWorkers(ctx context.Context) {
	log.Println("Monitoring workers...")

	// 如果数据库连接不可用，直接返回
	if s.workerRepo == nil {
		log.Println("Database connection not available, skipping worker monitoring")
		return
	}

	// 获取所有 worker
	workers, err := s.workerRepo.GetAll(nil)
	if err != nil {
		log.Printf("Failed to get workers from database: %v", err)
		return
	}

	// 遍历所有 worker
	for i := range workers {
		// 使用指针直接修改原始切片中的元素
		worker := &workers[i]

		// 为每个 worker 启动一个协程，避免一个 worker 响应慢影响其他 worker
		go func(w *models.WorkerInfo) {

			// 🔑 关键修复：获取该worker的专用锁，避免与资源分配更新冲突
			workerLock := s.getWorkerLock(w.WorkerID)
			workerLock.Lock()
			defer workerLock.Unlock()

			log.Printf("🔒 Acquired worker lock for %s, proceeding with resource monitoring update", w.WorkerID)

			// 🔑 重要：在锁保护下重新获取最新的worker信息，确保数据一致性
			// 避免使用可能过期的worker数据，特别是资源分配字段
			freshWorker, err := s.workerRepo.GetByID(w.WorkerID)
			if err != nil {
				log.Printf("Failed to get fresh worker info for monitoring: %v", err)
				return
			}
			log.Printf("🔄 Fetched fresh worker info from database for %s under lock protection", w.WorkerID)

			// 获取 worker 的资源情况（只获取资源使用率，不影响资源分配）
			nodesResource, err := s.getWorkerResource(ctx, &freshWorker)
			if err != nil {
				log.Printf("Failed to get worker resource info: %v", err)

				// 更新 worker 状态为 DISCONNECT
				if updateErr := s.workerRepo.UpdateStatus(w.WorkerID, "DISCONNECT"); updateErr != nil {
					log.Printf("Failed to update worker status: %v", updateErr)
				} else {
					log.Printf("Successfully updated worker %s status to DISCONNECT", w.WorkerID)
				}

				return
			}

			if len(*nodesResource) == 0 {
				log.Printf("No resource information available for worker %s", w.WorkerID)

				// 更新 worker 状态为 DISCONNECT
				if updateErr := s.workerRepo.UpdateStatus(w.WorkerID, "DISCONNECT"); updateErr != nil {
					log.Printf("Failed to update worker status: %v", updateErr)
				} else {
					log.Printf("Successfully updated worker %s status to DISCONNECT", w.WorkerID)
				}

				return
			}

			// 🔑 关键修复：只更新资源使用率，保留最新的资源分配值
			// 避免用旧的资源分配数据覆盖部署时更新的新数据
			for i := range freshWorker.Nodes {
				for j := range *nodesResource {
					if freshWorker.Nodes[i].IP == (*nodesResource)[j].IP {
						// 只更新资源使用率和硬件信息，保留资源分配
						freshWorker.Nodes[i].CPU = (*nodesResource)[j].CPU
						freshWorker.Nodes[i].Memory = (*nodesResource)[j].Memory
						freshWorker.Nodes[i].Disk = (*nodesResource)[j].Disk
						freshWorker.Nodes[i].Cpu_use_rate = (*nodesResource)[j].Cpu_use_rate
						freshWorker.Nodes[i].Memory_use_rate = (*nodesResource)[j].Memory_use_rate
						freshWorker.Nodes[i].Disk_use_rate = (*nodesResource)[j].Disk_use_rate
						// 🔑 重要：不更新 Cpu_allocation, Memory_allocation, Disk_allocation
						// 这些字段由部署和资源释放逻辑专门管理
						log.Printf("Updated resource usage for node %s: CPU=%.2f%%, Memory=%.2f%%, Disk=%.2f%% (allocation preserved)",
							freshWorker.Nodes[i].IP,
							freshWorker.Nodes[i].Cpu_use_rate*100,
							freshWorker.Nodes[i].Memory_use_rate*100,
							freshWorker.Nodes[i].Disk_use_rate*100)
						log.Printf("Node %s allocation details: CPU_allocation=%.2f, Memory_allocation=%d, Reserve_rate=%.2f",
							freshWorker.Nodes[i].IP,
							freshWorker.Nodes[i].Cpu_allocation,
							freshWorker.Nodes[i].Memory_allocation,
							freshWorker.Nodes[i].Reserve_rate)
						break
					}
				}
			}

			// 更新 worker 信息到数据库（在锁保护下）
			if updateErr := s.workerRepo.Update(freshWorker); updateErr != nil {
				log.Printf("Failed to update worker info: %v", updateErr)
			} else {
				log.Printf("Successfully updated worker %s resource usage (allocation preserved)", freshWorker.WorkerID)
			}

		}(worker)
	}
}

// getWorkerResource 获取 worker 的资源情况
func (s *Service) getWorkerResource(ctx context.Context, worker *models.WorkerInfo) (*NodesResourceResult, error) {
	log.Printf("Getting resource information for worker %s (%s)", worker.Name, worker.WorkerID)

	// 检查 worker 是否有节点
	if len(worker.Nodes) == 0 {
		log.Printf("Worker %s has no nodes, using empty resource list", worker.WorkerID)
		// 返回空切片而不是错误
		emptyResult := make(NodesResourceResult, 0)
		return &emptyResult, nil
	}

	// 创建资源列表
	resources := make([]models.Node, 0, len(worker.Nodes))

	// 遍历 worker 的所有节点
	for i := range worker.Nodes {
		// 使用指针直接修改原始切片中的元素
		node := &worker.Nodes[i]

		// 检查节点是否有 instance 字段
		if node.Instance == "" {
			log.Printf("Node %s has no instance field, skipping", node.IP)
			continue
		}

		// 获取节点硬件信息（CPU核数、内存MB、磁盘MB）
		hardwareInfo, err := utils.GetNodeHardwareInfo(node.Instance)
		if err != nil {
			log.Printf("Failed to get hardware info for node %s: %v", node.Instance, err)
			// 继续处理其他节点，不要因为一个节点失败而中断整个过程
			continue
		}

		// 获取节点 CPU 使用率（5分钟平均）
		// 注意：如果获取失败，函数会返回100%使用率，使节点不可用
		cpuUsage, err := utils.GetNodeCPUUsage(node.Instance)
		if err != nil {
			// 这种情况理论上不会发生，因为函数已经处理了错误情况
			log.Printf("Unexpected error getting CPU usage for node %s: %v", node.Instance, err)
			cpuUsage = 1.0 // 设为100%使节点不可用
		}

		// 获取节点内存使用率（5分钟平均）
		// 注意：如果获取失败，函数会返回100%使用率，使节点不可用
		memUsage, err := utils.GetNodeMemoryUsage(node.Instance)
		if err != nil {
			// 这种情况理论上不会发生，因为函数已经处理了错误情况
			log.Printf("Unexpected error getting memory usage for node %s: %v", node.Instance, err)
			memUsage = 1.0 // 设为100%使节点不可用
		}

		// 获取节点磁盘使用率（/user目录，5分钟平均）
		// 注意：如果获取失败，函数会返回100%使用率，使节点不可用
		diskUsage, err := utils.GetNodeDiskUsage(node.Instance)
		if err != nil {
			// 这种情况理论上不会发生，因为函数已经处理了错误情况
			log.Printf("Unexpected error getting disk usage for node %s: %v", node.Instance, err)
			diskUsage = 1.0 // 设为100%使节点不可用
		}

		// 创建资源节点，使用获取到的硬件信息更新节点配置
		resource := models.Node{
			IP:                node.IP,
			Instance:          node.Instance,
			Memory:            hardwareInfo.MemoryMB,
			CPU:               hardwareInfo.CPUCores,
			Disk:              hardwareInfo.DiskMB,
			Cpu_use_rate:      cpuUsage,
			Memory_use_rate:   memUsage,
			Disk_use_rate:     diskUsage,
			Reserve_rate:      node.Reserve_rate,
			Cpu_allocation:    node.Cpu_allocation,
			Memory_allocation: node.Memory_allocation,
			Disk_allocation:   node.Disk_allocation,
		}
		resources = append(resources, resource)

		log.Printf("Got complete resource info for node %s: CPU=%.1f核(%.2f%%), Memory=%dMB(%.2f%%), Disk=%dMB(%.2f%%)",
			node.Instance,
			hardwareInfo.CPUCores, cpuUsage*100,
			hardwareInfo.MemoryMB, memUsage*100,
			hardwareInfo.DiskMB, diskUsage*100)
	}

	// 返回资源信息
	return &resources, nil
}
