package service

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// DeployRecordRepository 定义部署记录仓库接口
type DeployRecordRepository interface {
	GetByID(id string) (models.DeployRecord, error)
}

// OSSManager 轻量级的 OSS 管理器，负责所有 OSS 相关操作
type OSSManager struct {
	deployRecordRepo DeployRecordRepository
	provider         OSSProvider
}

// 删除适配器层，直接使用 utils 层的实现

// NewOSSManager 创建新的 OSS 管理器实例
func NewOSSManager(deployRecordRepo DeployRecordRepository, providerType ...OSSProviderType) *OSSManager {
	// 默认使用阿里云
	var provider OSSProvider = &utils.AliOSSProvider{}

	// 如果指定了提供商类型
	if len(providerType) > 0 {
		switch providerType[0] {
		case OSSProviderAliyun:
			provider = &utils.AliOSSProvider{}
		case OSSProviderTencent:
			// TODO: 实现腾讯云提供商
			provider = &utils.AliOSSProvider{} // 暂时使用阿里云
		case OSSProviderHuawei:
			// TODO: 实现华为云提供商
			provider = &utils.AliOSSProvider{} // 暂时使用阿里云
		default:
			provider = &utils.AliOSSProvider{}
		}
	}

	return &OSSManager{
		deployRecordRepo: deployRecordRepo,
		provider:         provider,
	}
}

// GetServiceOSSStatus 获取指定服务的 OSS 状态数据（核心方法）
func (m *OSSManager) GetServiceOSSStatus(ctx context.Context, record models.DeployRecord) (*models.OSSStatusResult, error) {

	result := &models.OSSStatusResult{
		ServiceID: record.ServiceID,
		CheckTime: time.Now(),
		Success:   false,
	}

	// 解析 OSS 配置
	ossConfig, err := m.parseOSSConfigFromRecord(record)
	if err != nil {
		log.Printf("[OSSManager] 解析OSS配置失败: %v", err)
		result.ErrorMessage = err.Error()
		return result, err
	}

	// 测试 OSS 连接
	if err := m.testOSSConnection(ossConfig); err != nil {
		log.Printf("[OSSManager] OSS连接测试失败: %v", err)
		err = fmt.Errorf("OSS连接测试失败: %w", err)
		result.ErrorMessage = err.Error()
		return result, err
	}

	// 获取 OSS 统计信息
	ossUtilStats, err := m.getOSSStatistics(ossConfig)
	if err != nil {
		log.Printf("[OSSManager] 获取OSS统计信息失败: %v", err)
		result.ErrorMessage = err.Error()
		return result, err
	}

	// 构造成功结果
	result.Success = true
	result.OSSConfig = &models.OSSConfigInfo{
		Bucket:   ossConfig.GetBucketName(),
		Endpoint: ossConfig.GetEndpoint(),
		Region:   ossConfig.GetRegion(),
		FilePath: ossConfig.GetFilePath(),
	}

	// 直接使用 OSSUtil 统计数据（无需转换）
	result.OSSUtilStats = ossUtilStats

	log.Printf("[OSSManager] OSS状态检查完成: 服务=%s, 成功=%v", record.ServiceID, result.Success)
	return result, nil
}

// parseOSSConfigFromRecord 从部署记录中解析 OSS 配置（私有方法）
func (m *OSSManager) parseOSSConfigFromRecord(record models.DeployRecord) (OSSConfig, error) {
	// 构造 WorkerDeployRequest 以解析环境变量
	workerReq := &models.WorkerDeployRequest{
		ServiceId:    record.ServiceID,
		DomainPrefix: record.DomainPrefix,
		DomainSuffix: record.DomainSuffix,
		Labels:       record.Labels,
		CustomerEnvs: record.CustomerEnvs,
	}

	// 使用 utils 中的方法解析环境变量
	_, envMap, err := utils.ProcessEnvironmentVariablesFromRequest(workerReq)
	if err != nil {
		return nil, fmt.Errorf("解析环境变量失败: %w", err)
	}

	// 从环境变量中解析OSS配置
	ossConfig, err := utils.ParseAliOSSConfigFromEnvMap(envMap)
	if err != nil {
		return nil, fmt.Errorf("解析OSS配置失败: %w", err)
	}

	return ossConfig, nil
}

// testOSSConnection 测试 OSS 连接（私有方法）
func (m *OSSManager) testOSSConnection(ossConfig OSSConfig) error {
	return m.provider.TestConnection(ossConfig)
}

// getOSSStatistics 获取 OSS 统计信息（私有方法）
// 现在只使用 OSSUtil 工具方式，支持多云
func (m *OSSManager) getOSSStatistics(ossConfig OSSConfig) (*models.OSSUtilStats, error) {
	// 使用 OSSUtil 工具获取统计数据
	ossUtilStats, err := m.provider.GetBucketStatsWithUtil(ossConfig)
	if err != nil {
		log.Printf("[OSSManager] 获取OSSUtil磁盘使用量失败: %v", err)
		return nil, fmt.Errorf("获取OSS统计信息失败: %w", err)
	}

	log.Printf("[OSSManager] OSSUtil统计成功: 对象数=%d, 总大小=%.2fMB, 执行时间=%s",
		ossUtilStats.ObjectCount, ossUtilStats.TotalSizeMB, ossUtilStats.ExecutionTime)

	return ossUtilStats, nil
}
