package service

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

// Config 保存 service 的配置
type Config struct {
	// service 特定的配置项
	LogDir  string `yaml:"log_dir" json:"log_dir"`
	DBPath  string `yaml:"db_path" json:"db_path"`
	DBType  string `yaml:"db_type" json:"db_type"`
	APIPort string `yaml:"api_port" json:"api_port"`

	// 🆕 通知系统配置
	Notification NotificationConfig `yaml:"notification" json:"notification"`
}

// NotificationConfig 通知系统配置
type NotificationConfig struct {
	Feishu   FeishuNotificationConfig   `yaml:"feishu" json:"feishu"`
	WeChat   WeChatNotificationConfig   `yaml:"wechat" json:"wechat"`
	DingTalk DingTalkNotificationConfig `yaml:"dingtalk" json:"dingtalk"`
	Email    EmailNotificationConfig    `yaml:"email" json:"email"`
}

// FeishuNotificationConfig 飞书通知配置
type FeishuNotificationConfig struct {
	Enabled    bool              `yaml:"enabled" json:"enabled"`
	WebhookURL string            `yaml:"webhook_url" json:"webhook_url"`
	UserMap    map[string]string `yaml:"user_map" json:"user_map"`
}

// WeChatNotificationConfig 微信通知配置
type WeChatNotificationConfig struct {
	Enabled    bool   `yaml:"enabled" json:"enabled"`
	WebhookURL string `yaml:"webhook_url" json:"webhook_url"`
	AppID      string `yaml:"app_id" json:"app_id"`
	Secret     string `yaml:"secret" json:"secret"`
}

// DingTalkNotificationConfig 钉钉通知配置
type DingTalkNotificationConfig struct {
	Enabled     bool   `yaml:"enabled" json:"enabled"`
	WebhookURL  string `yaml:"webhook_url" json:"webhook_url"`
	AccessToken string `yaml:"access_token" json:"access_token"`
}

// EmailNotificationConfig 邮件通知配置
type EmailNotificationConfig struct {
	Enabled     bool     `yaml:"enabled" json:"enabled"`
	SMTPHost    string   `yaml:"smtp_host" json:"smtp_host"`
	SMTPPort    int      `yaml:"smtp_port" json:"smtp_port"`
	Username    string   `yaml:"username" json:"username"`
	Password    string   `yaml:"password" json:"password"`
	FromName    string   `yaml:"from_name" json:"from_name"`
	ToAddresses []string `yaml:"to_addresses" json:"to_addresses"`
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	Port string `yaml:"port" json:"port"`
	Host string `yaml:"host" json:"host"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Path string `yaml:"path" json:"path"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level string `yaml:"level" json:"level"`
	File  string `yaml:"file" json:"file"`
}

// DefaultConfig 返回默认的 service 配置
func DefaultConfig() Config {
	return Config{
		LogDir:  "log",
		DBPath:  "dbdata/service.db",
		DBType:  "sqlite",
		APIPort: "8080",
		// 默认通知配置
		Notification: NotificationConfig{
			Feishu: FeishuNotificationConfig{
				Enabled:    false,
				WebhookURL: "",
				UserMap:    make(map[string]string),
			},
			WeChat: WeChatNotificationConfig{
				Enabled:    false,
				WebhookURL: "",
				AppID:      "",
				Secret:     "",
			},
			DingTalk: DingTalkNotificationConfig{
				Enabled:     false,
				WebhookURL:  "",
				AccessToken: "",
			},
			Email: EmailNotificationConfig{
				Enabled:     false,
				SMTPHost:    "",
				SMTPPort:    587,
				Username:    "",
				Password:    "",
				FromName:    "Zero Ops System",
				ToAddresses: []string{},
			},
		},
	}
}

// LoadConfig 加载配置的主函数
// 优先级：1. config/ 文件夹内的配置文件 2. 默认配置
func LoadConfig() *Config {
	// 尝试加载配置文件
	configPath := getConfigFilePath()

	fmt.Println("======configfile path=====", configPath)

	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		fmt.Println("======configfile path not exist=====")
		// 配置文件不存在，使用默认配置
		config := DefaultConfig()
		return &config
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		// 读取失败，使用默认配置
		config := DefaultConfig()
		return &config
	}

	// 解析 YAML
	config := DefaultConfig() // 从默认配置开始，确保所有字段都有默认值
	if err := yaml.Unmarshal(data, &config); err != nil {
		// 解析失败，使用默认配置
		config := DefaultConfig()
		return &config
	}

	return &config
}

// getConfigFilePath 获取配置文件路径
// 简化逻辑：只从 config/ 目录读取
func getConfigFilePath() string {
	env := os.Getenv("ENV")
	switch env {
	case "dev", "development":
		return "config/config-dev.yaml"
	case "prod", "production":
		return "config/config-prod.yaml"
	case "staging", "test":
		return "config/config-staging.yaml"
	default:
		return "config/config.yaml"
	}
}
