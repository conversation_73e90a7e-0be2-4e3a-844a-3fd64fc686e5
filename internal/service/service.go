package service

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/zero-ops/service-system/internal/database"
	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/utils"
)

// Service represents the management node that interacts with users
type Service struct {
	// Database repositories
	db                *database.ServiceDB
	workerRepo        *database.WorkerRepository
	deployRecordRepo  *database.DeployRecordRepository
	imageTypeRepo     *database.ImageTypeRepository
	resourceUsageRepo *database.ServiceResourceUsageRepository

	// OSS 管理器
	ossManager *OSSManager

	// 队列处理器状态
	queueProcessorMu      sync.Mutex
	queueProcessorRunning bool
	queueProcessorCancel  context.CancelFunc

	// Worker资源分配锁管理 - 避免并发更新冲突
	workerLocksMu sync.RWMutex           // 保护workerLocks map的并发访问
	workerLocks   map[string]*sync.Mutex // 每个worker一个锁，key为workerID

	// 通知管理器 - 公共字段，其他包可以直接访问
	NotificationManager *utils.NotificationManager
}

// NewService creates a new service instance
func NewService() *Service {
	config := DefaultConfig()
	return NewServiceWithNewConfig(config)
}

// NewServiceWithNewConfig creates a new service instance with the given configuration
func NewServiceWithNewConfig(config Config) *Service {
	// Create service instance
	svc := &Service{
		queueProcessorMu:      sync.Mutex{},
		queueProcessorRunning: false,
		queueProcessorCancel:  nil,
		workerLocksMu:         sync.RWMutex{},
		workerLocks:           make(map[string]*sync.Mutex),
		NotificationManager: utils.CreateNotificationManagerFromConfig(utils.NotificationConfig{
			Feishu: utils.FeishuNotificationConfig{
				Enabled:    config.Notification.Feishu.Enabled,
				WebhookURL: config.Notification.Feishu.WebhookURL,
				UserMap:    config.Notification.Feishu.UserMap,
			},
			WeChat: utils.WeChatNotificationConfig{
				Enabled:    config.Notification.WeChat.Enabled,
				WebhookURL: config.Notification.WeChat.WebhookURL,
				AppID:      config.Notification.WeChat.AppID,
				Secret:     config.Notification.WeChat.Secret,
			},
			DingTalk: utils.DingTalkNotificationConfig{
				Enabled:     config.Notification.DingTalk.Enabled,
				WebhookURL:  config.Notification.DingTalk.WebhookURL,
				AccessToken: config.Notification.DingTalk.AccessToken,
			},
			Email: utils.EmailNotificationConfig{
				Enabled:     config.Notification.Email.Enabled,
				SMTPHost:    config.Notification.Email.SMTPHost,
				SMTPPort:    config.Notification.Email.SMTPPort,
				Username:    config.Notification.Email.Username,
				Password:    config.Notification.Email.Password,
				FromName:    config.Notification.Email.FromName,
				ToAddresses: config.Notification.Email.ToAddresses,
			},
		}), // 基于配置初始化通知管理器
	}

	// 记录通知管理器初始化状态
	total, enabled := svc.NotificationManager.GetNotifierCount()
	log.Printf("Notification manager initialized: %d total notifiers, %d enabled", total, enabled)
	if enabled > 0 {
		log.Printf("Enabled notifiers: %v", svc.NotificationManager.GetEnabledNotifiers())
	}

	// Initialize database if we're in service mode
	dbConfig := database.ServiceDBConfig{
		DBPath: config.DBPath,
	}

	db, err := database.NewServiceDB(dbConfig)
	if err != nil {
		log.Printf("Failed to initialize database: %v", err)
	} else {
		// Initialize schema
		if err := db.InitSchema(); err != nil {
			log.Printf("Failed to initialize database schema: %v", err)
		}

		// Initialize repositories
		svc.db = db
		svc.workerRepo = database.NewWorkerRepository(db)
		svc.deployRecordRepo = database.NewDeployRecordRepository(db)
		svc.imageTypeRepo = database.NewImageTypeRepository(db)
		svc.resourceUsageRepo = database.NewServiceResourceUsageRepository(db)

		// Initialize OSS manager
		svc.ossManager = NewOSSManager(svc.deployRecordRepo)
		log.Println("OSS manager initialized")

		// Start the queue processor
		svc.StartQueueProcessor(context.Background())
		log.Println("Queue processor started")

		// Start the worker monitor
		svc.StartWorkerMonitor(context.Background())
		log.Println("Worker monitor started")

		// Resource monitoring configuration
		log.Println("Resource monitoring configuration:")
		log.Printf("- Hardware resource monitoring: enabled (15 minutes interval)")
		if utils.GetEnvBool("ENABLE_OSS_MONITORING", false) {
			log.Printf("- OSS resource monitoring: enabled (2 hours interval)")
		} else {
			log.Printf("- OSS resource monitoring: disabled")
		}
	}

	return svc
}

// RestartService handles service restart requests from users
func (s *Service) RestartService(ctx context.Context, service_id string) (*models.ServiceResponse, error) {
	// Get deploy record from database
	var workerID string
	var serverIP string
	var record models.DeployRecord
	var found bool

	if s.deployRecordRepo != nil {
		// Try to find the deploy record
		var err error
		record, err = s.deployRecordRepo.GetByID(service_id)
		if err == nil {
			workerID = record.WorkerID
			found = true
		} else {
			log.Printf("Deploy record not found: %v", err)
			return &models.ServiceResponse{
				Code: 404,
				Msg:  fmt.Sprintf("无法找到部署记录: %v", err),
			}, fmt.Errorf("无法找到部署记录: %w", err)
		}
	} else {
		// 如果没有数据库连接，返回错误
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	// 如果没有分配 worker，返回错误
	if workerID == "" {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "部署记录中没有分配 worker",
		}, fmt.Errorf("部署记录中没有分配 worker")
	}

	// 从数据库获取 worker 信息
	workerInfo, err := s.workerRepo.GetByID(workerID)
	if err != nil {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  fmt.Sprintf("无法获取 worker 信息: %v", err),
		}, fmt.Errorf("无法获取 worker 信息: %w", err)
	}

	// 获取 server IP
	if len(workerInfo.Nodes) > 0 {
		serverIP = workerInfo.Nodes[0].IP
	}

	// 对于重启操作，不需要检查 Worker 状态
	// 重启操作不会增加资源占用，只是重新启动现有容器，即使 Worker 处于 FREEZE 状态也可以安全执行
	log.Printf("Restart operation for service %s on worker %s (status: %s)", record.ServiceID, workerID, workerInfo.Status)

	// 创建 worker 客户端
	worker := NewWorkerHTTPClient(workerInfo.Host)

	// Create a request with the necessary information
	workerReq := &models.WorkerDeployRequest{
		NodeIP:    serverIP,
		ServiceId: record.ServiceID, // 使用服务 ID 作为容器名称
	}

	// If we found the record, we can add more details
	if found {
		// Add any additional information from the record if needed
	}

	// 调用 worker 的 RestartContainer 函数
	workerResp, err := worker.RestartContainer(ctx, workerReq)
	if err != nil {
		return &models.ServiceResponse{
			Code: workerResp.Code,
			Data: workerResp.Data,
			Msg:  workerResp.Msg,
		}, err
	}

	// 调用成功后，更新部署记录状态为 PROCESSING
	if updateErr := s.deployRecordRepo.UpdateStatus(record.ServiceID, "PROCESSING"); updateErr != nil {
		log.Printf("Failed to update deploy record status: %v", updateErr)
		// 不中断流程，继续返回成功响应，但记录日志
	}

	// 转换为 ServiceResponse
	return &models.ServiceResponse{
		Code: workerResp.Code,
		Data: workerResp.Data,
		Msg:  workerResp.Msg,
	}, nil
}

// UpdateService handles service update requests from users
// 当前仅支持更新镜像名和镜像地址，其他参数保持不变
func (s *Service) UpdateService(ctx context.Context, req *models.UpdateServiceRequest) (*models.ServiceResponse, error) {
	log.Printf("Starting service update for service %s with new image %s", req.ServiceInfo.ServiceID, req.ServiceInfo.ImageName)
	// Get deploy record from database
	var workerID string
	var record models.DeployRecord

	if s.deployRecordRepo != nil {
		// Try to find the deploy record
		var err error
		record, err = s.deployRecordRepo.GetByID(req.ServiceInfo.ServiceID)
		if err == nil {
			workerID = record.WorkerID
		} else {
			log.Printf("Deploy record not found: %v", err)
			return &models.ServiceResponse{
				Code: 404,
				Msg:  fmt.Sprintf("无法找到部署记录: %v", err),
			}, fmt.Errorf("无法找到部署记录: %w", err)
		}
	} else {
		// 如果没有数据库连接，返回错误
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	// 如果没有分配 worker，返回错误
	if workerID == "" {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "部署记录中没有分配 worker",
		}, fmt.Errorf("部署记录中没有分配 worker")
	}

	// 从数据库获取 worker 信息
	workerInfo, err := s.workerRepo.GetByID(workerID)
	if err != nil {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  fmt.Sprintf("无法获取 worker 信息: %v", err),
		}, fmt.Errorf("无法获取 worker 信息: %w", err)
	}

	// 对于升级操作，不需要检查 Worker 状态
	// 升级操作不会增加资源占用，只是替换镜像，即使 Worker 处于 FREEZE 状态也可以安全执行
	log.Printf("Update operation for service %s on worker %s (status: %s)", req.ServiceInfo.ServiceID, workerID, workerInfo.Status)

	// 通过 ImageName 获取 ImageURL、Labels 和 Ports（更新镜像相关信息）
	var imageURL string
	var imageLabels []string
	var imagePorts []string
	if req.ServiceInfo.ImageName != "" {
		// 查询 image_type 表获取 ImageURL、Labels 和 Ports
		imageType, err := s.imageTypeRepo.GetByName(req.ServiceInfo.ImageName)
		if err != nil {
			log.Printf("Failed to get image information for image name %s: %v", req.ServiceInfo.ImageName, err)
			return &models.ServiceResponse{
				Code: 400,
				Msg:  fmt.Sprintf("无法获取镜像信息: %v", err),
			}, fmt.Errorf("无法获取镜像信息: %w", err)
		}
		imageURL = imageType.ImageURL
		imageLabels = imageType.Labels
		imagePorts = imageType.Ports
		log.Printf("Found image information for %s: URL=%s, Labels=%v, Ports=%v",
			req.ServiceInfo.ImageName, imageURL, imageLabels, imagePorts)
	}

	// 创建基础标签
	baseLabels := []string{
		"system=ops-system",
		"service-type=" + record.ServiceType,
	}

	// 将镜像标签与已存在的记录标签进行聚合，并进行排重处理
	allLabels := utils.MergeLabelsWithDeduplication(baseLabels, record.Labels, imageLabels)

	// 记录标签聚合过程和端口信息
	log.Printf("Label aggregation for service %s:", req.ServiceInfo.ServiceID)
	log.Printf("  Base labels: %v", baseLabels)
	log.Printf("  Image labels: %v", imageLabels)
	log.Printf("  Record labels: %v", record.Labels)
	log.Printf("  Final labels: %v", allLabels)
	log.Printf("  Image ports: %v", imagePorts)

	// 创建 worker 客户端
	worker := NewWorkerHTTPClient(workerInfo.Host)

	// Create a request with the necessary information for image update
	// 传递镜像更新所需的信息，包括聚合后的标签和端口信息
	workerReq := &models.WorkerDeployRequest{
		ServiceId: req.ServiceInfo.ServiceID,
		ImageName: req.ServiceInfo.ImageName, // 更新镜像名
		ImageURL:  imageURL,                  // 更新镜像地址
		Labels:    allLabels,                 // 传递聚合后的标签
		Ports:     imagePorts,                // 传递镜像端口信息

		// TODO: 扩展点 - 如需支持更多字段更新，可在此处添加
		// 例如：
		// DomainPrefix:    req.ServiceInfo.DomainPrefix,    // 域名前缀更新
		// DomainSuffix:    req.ServiceInfo.DomainSuffix,    // 域名后缀更新
		// Expiration:      req.ServiceInfo.Expiration,      // 过期时间更新
		// DurationSeconds: durationSeconds,                 // 持续时间更新
		// ApiReplica:      req.ServiceInfo.APIReplica,      // API副本数更新
		// ApiCpu:          req.ServiceInfo.APICPU,          // API CPU更新
		// ApiMemory:       req.ServiceInfo.APIMemory,       // API内存更新
		// AutoReplica:     req.ServiceInfo.AutoReplica,     // Auto副本数更新
		// AutoCpu:         req.ServiceInfo.AutoCPU,         // Auto CPU更新
		// AutoMemory:      req.ServiceInfo.AutoMemory,      // Auto内存更新
		// CustomerEnvs:    req.ServiceInfo.CustomerEnvs,    // 环境变量更新
	}

	// 获取 server IP
	if len(workerInfo.Nodes) > 0 {
		workerReq.NodeIP = workerInfo.Nodes[0].IP
	}

	workerResp, err := worker.UpdateContainer(ctx, workerReq)
	if err != nil {
		return &models.ServiceResponse{
			Code: workerResp.Code,
			Data: workerResp.Data,
			Msg:  workerResp.Msg,
		}, err
	}

	// 调用成功后，更新部署记录的状态和标签
	// 1. 更新状态为 PROCESSING
	// 2. 更新镜像名称（如果有变化）
	// 3. 更新聚合后的标签
	if updateErr := s.deployRecordRepo.UpdateStatus(record.ServiceID, "PROCESSING"); updateErr != nil {
		log.Printf("Failed to update deploy record status: %v", updateErr)
		// 不中断流程，继续返回成功响应，但记录日志
	}

	// 更新部署记录中的镜像名称和标签
	if req.ServiceInfo.ImageName != "" {
		record.ImageName = req.ServiceInfo.ImageName
	}
	record.Labels = allLabels
	record.Ports = imagePorts

	if updateErr := s.deployRecordRepo.Update(record); updateErr != nil {
		log.Printf("Failed to update deploy record with new image and labels: %v", updateErr)
		// 不中断流程，继续返回成功响应，但记录日志
	} else {
		log.Printf("Successfully updated deploy record for service %s with new image %s, %d labels, and %d ports",
			record.ServiceID, record.ImageName, len(allLabels), len(imagePorts))
	}

	// 转换为 ServiceResponse
	return &models.ServiceResponse{
		Code: workerResp.Code,
		Data: workerResp.Data,
		Msg:  workerResp.Msg,
	}, nil
}

// StopService stops a service on a worker node
func (s *Service) StopService(ctx context.Context, id string) (*models.ServiceResponse, error) {
	// Get deploy record from database
	var workerID string
	var serverIP string
	var record models.DeployRecord
	var found bool

	if s.deployRecordRepo != nil {
		// Try to find the deploy record
		var err error
		record, err = s.deployRecordRepo.GetByID(id)
		if err == nil {
			workerID = record.WorkerID
			found = true
		} else {
			log.Printf("Deploy record not found: %v", err)
			return &models.ServiceResponse{
				Code: 404,
				Msg:  fmt.Sprintf("无法找到部署记录: %v", err),
			}, fmt.Errorf("无法找到部署记录: %w", err)
		}
	} else {
		// 如果没有数据库连接，返回错误
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	// 如果没有分配 worker，返回错误
	if workerID == "" {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "部署记录中没有分配 worker",
		}, fmt.Errorf("部署记录中没有分配 worker")
	}

	// 从数据库获取 worker 信息
	workerInfo, err := s.workerRepo.GetByID(workerID)
	if err != nil {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  fmt.Sprintf("无法获取 worker 信息: %v", err),
		}, fmt.Errorf("无法获取 worker 信息: %w", err)
	}

	// 对于停止操作，不需要检查 Worker 状态
	// 停止操作会释放资源，即使 Worker 处于 FREEZE 状态也可以安全执行，甚至有助于释放资源
	log.Printf("Stop operation for service %s on worker %s (status: %s)", id, workerID, workerInfo.Status)

	// 获取 server IP
	if len(workerInfo.Nodes) > 0 {
		serverIP = workerInfo.Nodes[0].IP
	}

	// 创建 worker 客户端
	worker := NewWorkerHTTPClient(workerInfo.Host)

	// Create a request with the necessary information
	workerReq := &models.WorkerDeployRequest{
		NodeIP:    serverIP,
		ServiceId: id, // 使用服务 ID 作为容器名称
	}

	// If we found the record, we can add more details
	if found {
		// Add any additional information from the record if needed
	}

	// 调用 worker 的 StopContainer 函数
	workerResp, err := worker.StopContainer(ctx, workerReq)
	if err != nil {
		return &models.ServiceResponse{
			Code: workerResp.Code,
			Data: workerResp.Data,
			Msg:  workerResp.Msg,
		}, err
	}

	// 调用成功后，更新部署记录状态为 STOPPED
	if updateErr := s.deployRecordRepo.UpdateStatus(record.ServiceID, "STOPPED"); updateErr != nil {
		log.Printf("Failed to update deploy record status: %v", updateErr)
		// 不中断流程，继续返回成功响应，但记录日志
	}

	// 转换为 ServiceResponse
	return &models.ServiceResponse{
		Code: workerResp.Code,
		Data: workerResp.Data,
		Msg:  workerResp.Msg,
	}, nil
}

// GetWorkerList returns a list of all registered worker nodes with optional filtering
func (s *Service) GetWorkerList(ctx context.Context, filter *models.WorkerFilter) (*models.WorkerListResponse, error) {
	// 从数据库获取 worker 列表
	if s.workerRepo != nil {
		workers, err := s.workerRepo.GetAll(filter)
		if err != nil {
			log.Printf("Failed to get workers from database: %v", err)
			return &models.WorkerListResponse{
				Code:    500,
				Workers: nil,
				Msg:     fmt.Sprintf("无法从数据库获取 worker 列表: %v", err),
			}, fmt.Errorf("无法从数据库获取 worker 列表: %w", err)
		}

		return &models.WorkerListResponse{
			Code:    200,
			Workers: workers,
			Msg:     "Service: 获取Worker列表成功",
		}, nil
	}

	// 如果没有数据库连接，返回错误
	return &models.WorkerListResponse{
		Code:    500,
		Workers: nil,
		Msg:     "数据库连接不可用",
	}, fmt.Errorf("数据库连接不可用")
}

// RegisterWorker registers a new worker node with the service
func (s *Service) RegisterWorker(ctx context.Context, req *models.RegisterWorkerRequest) (*models.ServiceResponse, error) {
	// Generate a unique ID for the worker using ULID
	workerID, err := utils.GeneratePrefixedULID("worker")
	if err != nil {
		log.Printf("Failed to generate ULID for worker: %v", err)
		// Fallback to the old method if ULID generation fails
		workerID = fmt.Sprintf("worker%d", time.Now().Unix())
	}

	// Convert nodes array to Node struct array
	var nodes []models.Node
	nodes = append(nodes, req.Nodes...)

	// Create worker info
	workerInfo := models.WorkerInfo{
		WorkerID:     workerID,
		Name:         req.Name,
		ServerType:   req.ServerType,
		Labels:       req.Labels,
		Host:         req.Host,
		HostIP:       req.HostIP,
		DomainSuffix: req.DomainSuffix,
		Nodes:        nodes,
		Status:       "AVAILABLE",
	}

	// 存储到数据库
	if s.workerRepo != nil {
		if err := s.workerRepo.Create(workerInfo); err != nil {
			log.Printf("Failed to store worker in database: %v", err)
			return &models.ServiceResponse{
				Code: 500,
				Msg:  fmt.Sprintf("无法将 worker 保存到数据库: %v", err),
			}, fmt.Errorf("无法将 worker 保存到数据库: %w", err)
		}
	} else {
		// 如果没有数据库连接，返回错误
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "数据库连接不可用",
		}, fmt.Errorf("数据库连接不可用")
	}

	return &models.ServiceResponse{
		Code: 200,
		Data: gin.H{"workerId": workerID},
		Msg:  "Service: Worker注册成功",
	}, nil
}

// RemoveWorker removes a worker node from the service
func (s *Service) RemoveWorker(ctx context.Context, worker_id string) (*models.ServiceResponse, error) {
	// 从数据库中删除 worker
	if s.workerRepo != nil {
		if err := s.workerRepo.Delete(worker_id); err != nil {
			log.Printf("Failed to remove worker from database: %v", err)
			return &models.ServiceResponse{
				Code: 404,
				Msg:  "Worker not found",
			}, fmt.Errorf("worker not found: %s", worker_id)
		}

		return &models.ServiceResponse{
			Code: 200,
			Msg:  "Service: Worker移除成功",
		}, nil
	}

	// 如果没有数据库连接，返回错误
	return &models.ServiceResponse{
		Code: 500,
		Msg:  "数据库连接不可用",
	}, fmt.Errorf("数据库连接不可用")
}

// UpdateWorker updates a worker node
func (s *Service) UpdateWorker(ctx context.Context, worker_id string, req *models.UpdateWorkerRequest) (*models.ServiceResponse, error) {
	// 从数据库获取 worker 信息
	if s.workerRepo != nil {
		worker, err := s.workerRepo.GetByID(worker_id)
		if err != nil {
			return &models.ServiceResponse{
				Code: 404,
				Msg:  "Worker not found",
			}, fmt.Errorf("worker not found: %s", worker_id)
		}

		// 添加调试日志
		log.Printf("UpdateWorker: worker_id=%s", worker_id)
		log.Printf("UpdateWorker: 更新前 worker.Nodes 数量=%d", len(worker.Nodes))
		if len(worker.Nodes) > 0 {
			for i, node := range worker.Nodes {
				log.Printf("UpdateWorker: 更新前 node[%d]: IP=%s, Instance=%s", i, node.IP, node.Instance)
			}
		}
		log.Printf("UpdateWorker: 请求中 req.Nodes 数量=%d", len(req.Nodes))
		if len(req.Nodes) > 0 {
			for i, node := range req.Nodes {
				log.Printf("UpdateWorker: 请求中 node[%d]: IP=%s, Instance=%s", i, node.IP, node.Instance)
			}
		}

		// 更新 worker 信息
		if req.Name != "" {
			worker.Name = req.Name
		}
		if req.ServerType != "" {
			worker.ServerType = req.ServerType
		}
		if req.Labels != nil {
			worker.Labels = req.Labels
		}
		if req.Host != "" {
			worker.Host = req.Host
		}
		if req.DomainSuffix != "" {
			worker.DomainSuffix = req.DomainSuffix
		}
		if req.Nodes != nil {
			// 创建一个map来存储现有节点，以IP为键
			existingNodes := make(map[string]models.Node)
			for _, node := range worker.Nodes {
				existingNodes[node.IP] = node
			}

			// 更新或添加请求中的节点
			for _, newNode := range req.Nodes {
				existingNodes[newNode.IP] = newNode
			}

			// 将map转换回切片
			worker.Nodes = []models.Node{}
			for _, node := range existingNodes {
				worker.Nodes = append(worker.Nodes, node)
			}

			log.Printf("UpdateWorker: 更新后 worker.Nodes 数量=%d", len(worker.Nodes))
			if len(worker.Nodes) > 0 {
				for i, node := range worker.Nodes {
					log.Printf("UpdateWorker: 更新后 node[%d]: IP=%s, Instance=%s", i, node.IP, node.Instance)
				}
			}
		}
		if req.Status != "" {
			worker.Status = req.Status
		}

		// 更新数据库中的 worker 信息
		if err := s.workerRepo.Update(worker); err != nil {
			log.Printf("Failed to update worker in database: %v", err)
			return &models.ServiceResponse{
				Code: 500,
				Msg:  fmt.Sprintf("无法更新数据库中的 worker: %v", err),
			}, fmt.Errorf("无法更新数据库中的 worker: %w", err)
		}

		return &models.ServiceResponse{
			Code: 200,
			Msg:  "Service: Worker更新成功",
		}, nil
	}

	// 如果没有数据库连接，返回错误
	return &models.ServiceResponse{
		Code: 500,
		Msg:  "数据库连接不可用",
	}, fmt.Errorf("数据库连接不可用")
}

// GetResourceStatus 获取服务资源使用状态（支持批量查询）
func (s *Service) GetResourceStatus(ctx context.Context, serviceIDs []string) (*models.ServiceResponse, error) {
	log.Printf("开始获取服务资源使用状态: %v", serviceIDs)

	// 检查输入参数
	if len(serviceIDs) == 0 {
		return &models.ServiceResponse{
			Code: 400,
			Msg:  "service_ids 不能为空",
		}, fmt.Errorf("service_ids 不能为空")
	}

	// 检查资源使用统计仓库
	if s.resourceUsageRepo == nil {
		return &models.ServiceResponse{
			Code: 500,
			Msg:  "资源使用统计仓库不可用",
		}, fmt.Errorf("资源使用统计仓库不可用")
	}

	// 批量获取服务资源使用统计（性能优化：一次查询所有数据）
	resourceUsageMap, err := s.resourceUsageRepo.GetByServiceIDs(serviceIDs)
	if err != nil {
		log.Printf("批量获取服务资源使用统计失败: %v", err)
		return &models.ServiceResponse{
			Code: 500,
			Msg:  fmt.Sprintf("批量获取服务资源使用统计失败: %v", err),
		}, fmt.Errorf("批量获取服务资源使用统计失败: %w", err)
	}

	// 处理查询结果
	var results []interface{}
	var notFoundServices []string

	for _, serviceID := range serviceIDs {
		resourceUsage, found := resourceUsageMap[serviceID]
		if !found {
			log.Printf("未找到服务资源使用统计: %s", serviceID)
			notFoundServices = append(notFoundServices, serviceID)
			continue
		}

		// 添加 service_id 字段到结果中，便于前端识别
		resourceData := map[string]interface{}{
			"service_id":                 serviceID,
			"cpu_usage":                  resourceUsage.CPUUsage,
			"memory_usage":               resourceUsage.MemoryUsage,
			"disk_usage":                 resourceUsage.DiskUsage,
			"oss_disk_usage":             resourceUsage.OSSDiskUsage,
			"oss_network_traffic":        resourceUsage.OSSNetworkTraffic,
			"oss_network_traffic_by_day": resourceUsage.OSSNetworkTrafficByDay,
			"network_in_traffic":         resourceUsage.NetworkInTraffic,
			"network_out_traffic":        resourceUsage.NetworkOutTraffic,
			"network_traffic_updated_at": resourceUsage.NetworkTrafficUpdatedAt,
			"last_access_time":           resourceUsage.LastAccessTime,
			"metadata":                   resourceUsage.Metadata,
			"created_at":                 resourceUsage.CreatedAt,
			"updated_at":                 resourceUsage.UpdatedAt,
		}

		results = append(results, resourceData)
	}

	// 构建响应数据
	responseData := map[string]interface{}{
		"total":   len(serviceIDs),
		"success": len(results),
		"results": results,
	}

	// 添加未找到的服务信息（如果有）
	if len(notFoundServices) > 0 {
		responseData["not_found"] = notFoundServices
	}

	// 构建响应消息
	var msg string
	if len(results) == len(serviceIDs) {
		msg = "获取服务资源使用状态成功"
	} else if len(results) > 0 {
		msg = fmt.Sprintf("部分获取成功：成功 %d 个，未找到 %d 个", len(results), len(notFoundServices))
	} else {
		msg = "未找到任何服务的资源使用统计"
	}

	log.Printf("批量获取服务资源使用状态完成: 总数=%d, 成功=%d, 未找到=%d",
		len(serviceIDs), len(results), len(notFoundServices))

	return &models.ServiceResponse{
		Code: 200,
		Data: responseData,
		Msg:  msg,
	}, nil
}

// GetServiceWaitingTime 获取服务排队等待时间
func (s *Service) GetServiceWaitingTime(ctx context.Context, serviceID string) (*models.ServiceResponse, error) {
	// 获取目标服务记录
	targetRecord, err := s.deployRecordRepo.GetByID(serviceID)
	if err != nil {
		return nil, fmt.Errorf("获取服务记录失败: %w", err)
	}

	// 检查服务状态
	if targetRecord.Status != "QUEUEING" {
		return &models.ServiceResponse{
			Code: 200,
			Msg:  fmt.Sprintf("服务不在排队中，当前状态为: %s", targetRecord.Status),
			Data: &models.WaitingTimeResponse{
				ServiceID:         serviceID,
				ServerType:        targetRecord.ServiceType,
				CurrentPosition:   0,
				EstimatedWaitTime: 0,
			},
		}, nil
	}

	// 计算排队等待时间
	waitingTime, err := s.calculateWaitingTime(ctx, targetRecord)
	if err != nil {
		return nil, fmt.Errorf("计算等待时间失败: %w", err)
	}

	return &models.ServiceResponse{
		Code: 200,
		Msg:  "获取排队等待时间成功",
		Data: waitingTime,
	}, nil
}

// calculateWaitingTime 计算服务的排队等待时间
// TODO: 优化代码，更具逻辑
func (s *Service) calculateWaitingTime(ctx context.Context, targetRecord models.DeployRecord) (*models.WaitingTimeResponse, error) {
	// 1. 获取同 server_type 的所有 QUEUEING 记录，按 updated_at 排序
	queueingRecords, err := s.getQueueingRecordsByServerType(targetRecord.ServiceType)
	if err != nil {
		return nil, fmt.Errorf("获取排队记录失败: %w", err)
	}

	// 2. 找到目标服务在队列中的位置
	targetPosition := -1
	for i, record := range queueingRecords {
		if record.ServiceID == targetRecord.ServiceID {
			targetPosition = i
			break
		}
	}

	if targetPosition == -1 {
		return nil, fmt.Errorf("目标服务不在排队队列中")
	}

	// 3. 获取可用的 Worker 节点资源
	availableResources, err := s.getAvailableResources(targetRecord.ServiceType)
	if err != nil {
		return nil, fmt.Errorf("获取可用资源失败: %w", err)
	}

	// 4. 模拟排队过程，计算等待时间
	result := s.simulateQueueProcessing(queueingRecords, targetPosition, availableResources, targetRecord)

	return result, nil
}

// getQueueingRecordsByServerType 获取指定服务类型的排队记录
func (s *Service) getQueueingRecordsByServerType(serverType string) ([]models.DeployRecord, error) {
	// 获取所有 QUEUEING 状态的记录
	allQueueingRecords, err := s.deployRecordRepo.GetRecordsByStatus([]string{"QUEUEING"})
	if err != nil {
		return nil, err
	}

	// 过滤出相同 server_type 的记录
	var filteredRecords []models.DeployRecord
	for _, record := range allQueueingRecords {
		if record.ServiceType == serverType {
			filteredRecords = append(filteredRecords, record)
		}
	}

	// 按 updated_at 排序（已经在 GetRecordsByStatus 中排序了）
	return filteredRecords, nil
}

// AvailableResourceInfo 表示可用资源信息
type AvailableResourceInfo struct {
	TotalCPU        float64
	TotalMemory     int
	AvailableCPU    float64
	AvailableMemory int
	WorkerNodes     []models.WorkerInfo
}

// getAvailableResources 获取指定服务类型的可用资源
func (s *Service) getAvailableResources(serverType string) (*AvailableResourceInfo, error) {
	// 获取所有可用的 Worker
	workers, err := s.workerRepo.GetAll(&models.WorkerFilter{})
	if err != nil {
		return nil, fmt.Errorf("获取 Worker 列表失败: %w", err)
	}

	// 过滤出匹配服务类型的 Worker
	var matchingWorkers []models.WorkerInfo
	for _, worker := range workers {
		if worker.ServerType == serverType && worker.Status == "AVAILABLE" {
			matchingWorkers = append(matchingWorkers, worker)
		}
	}

	if len(matchingWorkers) == 0 {
		return &AvailableResourceInfo{}, nil
	}

	// 计算总资源和可用资源
	var totalCPU, availableCPU float64
	var totalMemory, availableMemory int

	for _, worker := range matchingWorkers {
		for _, node := range worker.Nodes {
			// 总资源
			totalCPU += node.CPU
			totalMemory += node.Memory

			// 可用资源 = 总资源 × (1 - 使用率) - 预留资源 - 已分配资源
			const cpuReserved = 0.5
			const memoryReservedMB = 500

			nodeCPU := node.CPU*(1-node.Cpu_use_rate) - cpuReserved - node.Cpu_allocation
			nodeMemory := int(float64(node.Memory)*(1-node.Memory_use_rate)) - memoryReservedMB - node.Memory_allocation

			// 确保不为负数
			if nodeCPU > 0 {
				availableCPU += nodeCPU
			}
			if nodeMemory > 0 {
				availableMemory += nodeMemory
			}
		}
	}

	return &AvailableResourceInfo{
		TotalCPU:        totalCPU,
		TotalMemory:     totalMemory,
		AvailableCPU:    availableCPU,
		AvailableMemory: availableMemory,
		WorkerNodes:     matchingWorkers,
	}, nil
}

// simulateQueueProcessing 模拟排队处理过程
// 🚀 优化版本：按照node级别进行资源分配模拟，避免资源碎片问题
func (s *Service) simulateQueueProcessing(queueingRecords []models.DeployRecord, targetPosition int, availableResources *AvailableResourceInfo, targetRecord models.DeployRecord) *models.WaitingTimeResponse {
	// 常量定义
	const (
		deployTimeWithResource    = 6    // 有资源时的部署时间（分钟）
		deployTimeWithoutResource = 720  // 无资源时的等待时间（12小时=720分钟）
		edgeCaseWaitTime          = 7200 // 边界情况等待时间（120小时=7200分钟）
	)

	// 初始化响应
	response := &models.WaitingTimeResponse{
		ServiceID:         targetRecord.ServiceID,
		ServerType:        targetRecord.ServiceType,
		CurrentPosition:   targetPosition + 1, // 位置从1开始
		EstimatedWaitTime: edgeCaseWaitTime,   // 默认返回120小时
	}

	// 边界情况检查
	if targetPosition < 0 || targetPosition >= len(queueingRecords) {
		log.Printf("⚠️  目标位置无效: position=%d, queueLength=%d", targetPosition, len(queueingRecords))
		return response
	}

	if len(availableResources.WorkerNodes) == 0 {
		log.Printf("⚠️  没有可用的worker节点")
		return response
	}

	// 截取需要模拟的队列：从第一个到目标位置（包含目标记录）
	simulationQueue := queueingRecords[:targetPosition+1]
	log.Printf("🎯 开始模拟排队处理: 队列长度=%d, 目标位置=%d", len(simulationQueue), targetPosition)

	// 创建worker节点的虚拟副本，用于模拟资源分配
	virtualWorkers := s.createVirtualWorkers(availableResources.WorkerNodes)

	// 模拟处理队列中的每个记录
	resourceInsufficientFromIndex := -1
	for i, record := range simulationQueue {
		log.Printf("📋 处理队列位置 %d: 服务 %s (类型: %s)", i, record.ServiceID, record.ServiceType)

		// 计算资源需求
		cpuNeed := record.ApiCpu * float64(record.ApiReplica)
		memoryNeed := record.ApiMemory * record.ApiReplica

		log.Printf("  💾 资源需求: CPU=%.2f cores (%.2f × %d), Memory=%d MB (%d × %d)",
			cpuNeed, record.ApiCpu, record.ApiReplica, memoryNeed, record.ApiMemory, record.ApiReplica)

		// 尝试在虚拟worker中分配资源
		if s.tryAllocateResourceInVirtualWorkers(virtualWorkers, record.ServiceType, cpuNeed, memoryNeed) {
			log.Printf("  ✅ 资源分配成功")
		} else {
			log.Printf("  ❌ 资源分配失败，从位置 %d 开始资源不足", i)
			resourceInsufficientFromIndex = i
			break
		}
	}

	// 计算总等待时间
	totalWaitTime := s.calculateTotalWaitTime(len(simulationQueue), resourceInsufficientFromIndex, deployTimeWithResource, deployTimeWithoutResource)

	response.EstimatedWaitTime = totalWaitTime
	log.Printf("🎉 排队模拟完成: 总等待时间=%d分钟 (%.1f小时)", totalWaitTime, float64(totalWaitTime)/60)

	return response
}

// createVirtualWorkers 创建worker节点的虚拟副本，用于模拟资源分配
// 返回的虚拟worker可以在内存中修改，不会影响原始数据
func (s *Service) createVirtualWorkers(workers []models.WorkerInfo) []models.WorkerInfo {
	virtualWorkers := make([]models.WorkerInfo, len(workers))

	for i, worker := range workers {
		// 深拷贝worker信息
		virtualWorkers[i] = models.WorkerInfo{
			ID:           worker.ID,
			WorkerID:     worker.WorkerID,
			Name:         worker.Name,
			ServerType:   worker.ServerType,
			Labels:       worker.Labels,
			Host:         worker.Host,
			HostIP:       worker.HostIP,
			DomainSuffix: worker.DomainSuffix,
			Status:       worker.Status,
			CreatedAt:    worker.CreatedAt,
			UpdatedAt:    worker.UpdatedAt,
			Nodes:        make([]models.Node, len(worker.Nodes)),
		}

		// 深拷贝nodes信息
		for j, node := range worker.Nodes {
			virtualWorkers[i].Nodes[j] = models.Node{
				IP:                node.IP,
				Instance:          node.Instance,
				Memory:            node.Memory,
				Memory_use_rate:   node.Memory_use_rate,
				CPU:               node.CPU,
				Cpu_use_rate:      node.Cpu_use_rate,
				Disk:              node.Disk,
				Disk_use_rate:     node.Disk_use_rate,
				Reserve_rate:      node.Reserve_rate,
				Cpu_allocation:    node.Cpu_allocation,    // 这个会在模拟中被修改
				Memory_allocation: node.Memory_allocation, // 这个会在模拟中被修改
				Disk_allocation:   node.Disk_allocation,
			}
		}
	}

	log.Printf("🔄 创建了 %d 个虚拟worker副本，共 %d 个节点", len(virtualWorkers), s.countTotalNodes(virtualWorkers))
	return virtualWorkers
}

// countTotalNodes 计算所有worker的总节点数
func (s *Service) countTotalNodes(workers []models.WorkerInfo) int {
	total := 0
	for _, worker := range workers {
		total += len(worker.Nodes)
	}
	return total
}

// tryAllocateResourceInVirtualWorkers 尝试在虚拟worker中分配资源
// 返回true表示分配成功，false表示资源不足
func (s *Service) tryAllocateResourceInVirtualWorkers(virtualWorkers []models.WorkerInfo, serviceType string, cpuNeed float64, memoryNeed int) bool {
	// 查找匹配服务类型的worker
	for i := range virtualWorkers {
		worker := &virtualWorkers[i]

		// 检查worker类型是否匹配
		if worker.ServerType != serviceType {
			continue
		}

		log.Printf("    🔍 检查worker: %s (类型: %s)", worker.Name, worker.ServerType)

		// 遍历worker的所有节点，寻找可用资源
		for j := range worker.Nodes {
			node := &worker.Nodes[j]

			// 计算节点的可用资源
			// 可用资源 = 总资源 × (1 - 使用率) - 预留资源 - 已分配资源
			const cpuReserved = 0.5
			const memoryReservedMB = 500

			availableCPU := node.CPU*(1-node.Cpu_use_rate) - cpuReserved - node.Cpu_allocation
			availableMemory := int(float64(node.Memory)*(1-node.Memory_use_rate)) - memoryReservedMB - node.Memory_allocation

			// 确保可用资源不为负数
			if availableCPU < 0 {
				availableCPU = 0
			}
			if availableMemory < 0 {
				availableMemory = 0
			}

			log.Printf("      📊 节点 %s: 可用CPU=%.2f cores, 可用内存=%d MB",
				node.IP, availableCPU, availableMemory)

			// 检查是否有足够的资源
			if availableCPU >= cpuNeed && availableMemory >= memoryNeed {
				// 资源充足，进行虚拟分配
				node.Cpu_allocation += cpuNeed
				node.Memory_allocation += memoryNeed

				log.Printf("      ✅ 在节点 %s 分配资源成功: CPU=%.2f, Memory=%d",
					node.IP, cpuNeed, memoryNeed)
				log.Printf("      📈 节点 %s 更新后: CPU分配=%.2f, 内存分配=%d",
					node.IP, node.Cpu_allocation, node.Memory_allocation)

				return true
			} else {
				log.Printf("      ❌ 节点 %s 资源不足: 需要CPU=%.2f(可用%.2f), 需要内存=%d(可用%d)",
					node.IP, cpuNeed, availableCPU, memoryNeed, availableMemory)
			}
		}
	}

	log.Printf("    ❌ 没有找到匹配类型 %s 的可用worker或资源不足", serviceType)
	return false
}

// calculateTotalWaitTime 计算总等待时间
// queueLength: 队列总长度
// resourceInsufficientFromIndex: 资源不足开始的位置，-1表示所有记录都有资源
// timeWithResource: 有资源时每个记录的处理时间
// timeWithoutResource: 无资源时每个记录的等待时间
func (s *Service) calculateTotalWaitTime(queueLength int, resourceInsufficientFromIndex int, timeWithResource int, timeWithoutResource int) int {
	if resourceInsufficientFromIndex == -1 {
		// 所有记录都能分配到资源
		totalTime := queueLength * timeWithResource
		log.Printf("📊 时间计算: 所有 %d 个记录都有资源，总时间 = %d × %d = %d 分钟",
			queueLength, queueLength, timeWithResource, totalTime)
		return totalTime
	} else {
		// 部分记录资源不足
		sufficientCount := resourceInsufficientFromIndex
		insufficientCount := queueLength - resourceInsufficientFromIndex

		sufficientTime := sufficientCount * timeWithResource
		insufficientTime := insufficientCount * timeWithoutResource
		totalTime := sufficientTime + insufficientTime

		log.Printf("📊 时间计算: 有资源 %d 个记录(%d分钟), 无资源 %d 个记录(%d分钟), 总时间 = %d 分钟",
			sufficientCount, sufficientTime, insufficientCount, insufficientTime, totalTime)
		return totalTime
	}
}
