package utils

import (
	"crypto/rand"
	"encoding/binary"
	"fmt"
	"io"
	"sync"
	"time"
)

// MaxUint48 是一个 48 位无符号整数的最大值
const MaxUint48 = (1 << 48) - 1

// ULID 类型表示一个 ULID (Universally Unique Lexicographically Sortable Identifier)
// ULID 是一个 128 位的标识符，由 48 位的时间戳和 80 位的随机数组成
// 格式: ttttttttttrrrrrrrrrrrrrrrrr (t=时间戳, r=随机数)
// 参考: https://github.com/ulid/spec
type ULID [16]byte

// 用于生成随机部分的熵源
var entropySource = rand.Reader

// 用于保护随机数生成的互斥锁
var entropyMu sync.Mutex

// 用于 Base32 编码的字符集 (使用小写字母，兼容 Elasticsearch 索引名称)
const encodeBase32 = "0123456789abcdefghjkmnpqrstvwxyz"

// 解码表
var decodeBase32 [256]byte

// 初始化解码表
func init() {
	for i := range decodeBase32 {
		decodeBase32[i] = 0xFF
	}
	for i := range encodeBase32 {
		decodeBase32[encodeBase32[i]] = byte(i)
	}
	// 为了向后兼容，同时支持大写字母的解码
	// 这样既可以生成小写的 ULID，也可以解析大写的 ULID
	upperCaseChars := "ABCDEFGHJKMNPQRSTVWXYZ"
	lowerCaseChars := "abcdefghjkmnpqrstvwxyz"
	for i := 0; i < len(upperCaseChars); i++ {
		// 将大写字母映射到对应的小写字母的值
		upperChar := upperCaseChars[i]
		lowerChar := lowerCaseChars[i]
		decodeBase32[upperChar] = decodeBase32[lowerChar]
	}
}

// String 返回 ULID 的字符串表示 (26个字符的 Base32 编码)
func (id ULID) String() string {
	var str [26]byte

	// 编码时间戳部分 (10 个字符)
	str[0] = encodeBase32[(id[0]&224)>>5]
	str[1] = encodeBase32[id[0]&31]
	str[2] = encodeBase32[(id[1]&224)>>5]
	str[3] = encodeBase32[id[1]&31]
	str[4] = encodeBase32[(id[2]&224)>>5]
	str[5] = encodeBase32[id[2]&31]
	str[6] = encodeBase32[(id[3]&224)>>5]
	str[7] = encodeBase32[id[3]&31]
	str[8] = encodeBase32[(id[4]&224)>>5]
	str[9] = encodeBase32[id[4]&31]

	// 编码随机部分 (16 个字符)
	str[10] = encodeBase32[(id[5]&224)>>5]
	str[11] = encodeBase32[id[5]&31]
	str[12] = encodeBase32[(id[6]&224)>>5]
	str[13] = encodeBase32[id[6]&31]
	str[14] = encodeBase32[(id[7]&224)>>5]
	str[15] = encodeBase32[id[7]&31]
	str[16] = encodeBase32[(id[8]&224)>>5]
	str[17] = encodeBase32[id[8]&31]
	str[18] = encodeBase32[(id[9]&224)>>5]
	str[19] = encodeBase32[id[9]&31]
	str[20] = encodeBase32[(id[10]&224)>>5]
	str[21] = encodeBase32[id[10]&31]
	str[22] = encodeBase32[(id[11]&224)>>5]
	str[23] = encodeBase32[id[11]&31]
	str[24] = encodeBase32[(id[12]&224)>>5]
	str[25] = encodeBase32[id[12]&31]

	return string(str[:])
}

// Bytes 返回 ULID 的字节表示
func (id ULID) Bytes() []byte {
	return id[:]
}

// Time 从 ULID 中提取时间戳
func (id ULID) Time() time.Time {
	msec := binary.BigEndian.Uint64(id[:8]) >> 16
	return time.Unix(int64(msec)/1000, (int64(msec)%1000)*int64(time.Millisecond))
}

// NewULID 生成一个新的 ULID
// 使用当前时间和加密安全的随机数
func NewULID() (ULID, error) {
	return NewULIDWithTime(time.Now())
}

// NewULIDWithTime 使用指定的时间生成 ULID
func NewULIDWithTime(t time.Time) (ULID, error) {
	var id ULID

	// 获取毫秒级时间戳
	ms := uint64(t.UnixNano() / int64(time.Millisecond))
	if ms > MaxUint48 {
		ms = MaxUint48
	}

	// 将时间戳编码到前 6 个字节
	binary.BigEndian.PutUint64(id[:8], ms<<16)

	// 生成随机部分 (10 个字节)
	entropyMu.Lock()
	_, err := io.ReadFull(entropySource, id[6:])
	entropyMu.Unlock()
	if err != nil {
		return id, err
	}

	return id, nil
}

// NewULIDString 生成一个新的 ULID 并返回其字符串表示
func NewULIDString() (string, error) {
	id, err := NewULID()
	if err != nil {
		return "", err
	}
	return id.String(), nil
}

// ParseULID 从字符串解析 ULID
func ParseULID(s string) (ULID, error) {
	var id ULID

	if len(s) != 26 {
		return id, fmt.Errorf("ulid: incorrect length: %d", len(s))
	}

	// 解码时间戳部分 (10 个字符)
	for i := 0; i < 10; i++ {
		v := decodeBase32[s[i]]
		if v == 0xFF {
			return id, fmt.Errorf("ulid: invalid character: %c", s[i])
		}

		if i%2 == 0 {
			id[i/2] |= v << 5
		} else {
			id[i/2] |= v
		}
	}

	// 解码随机部分 (16 个字符)
	for i := 10; i < 26; i++ {
		v := decodeBase32[s[i]]
		if v == 0xFF {
			return id, fmt.Errorf("ulid: invalid character: %c", s[i])
		}

		if i%2 == 0 {
			id[i/2-2] |= v << 5
		} else {
			id[i/2-2] |= v
		}
	}

	return id, nil
}

// GenerateULID 是一个通用函数，生成 ULID 字符串
// 这是一个便捷函数，与 uuid.go 中的 GenerateUUID 类似
func GenerateULID() (string, error) {
	return NewULIDString()
}

// GenerateMonotonicULID 生成一个单调递增的 ULID
// 如果在同一毫秒内多次调用，随机部分会递增
var (
	lastTime    uint64
	lastRandom  [10]byte
	monotonicMu sync.Mutex
)

// GenerateMonotonicULID 生成一个单调递增的 ULID
func GenerateMonotonicULID() (string, error) {
	monotonicMu.Lock()
	defer monotonicMu.Unlock()

	var id ULID

	// 获取当前时间戳
	now := time.Now()
	ms := uint64(now.UnixNano() / int64(time.Millisecond))
	if ms > MaxUint48 {
		ms = MaxUint48
	}

	// 将时间戳编码到前 6 个字节
	binary.BigEndian.PutUint64(id[:8], ms<<16)

	// 检查是否与上次时间相同
	if ms == lastTime {
		// 增加随机部分
		i := 9
		for i >= 0 {
			if lastRandom[i] == 255 {
				lastRandom[i] = 0
				i--
				continue
			}
			lastRandom[i]++
			break
		}
		// 如果溢出，使用新的随机数
		if i < 0 {
			entropyMu.Lock()
			_, err := io.ReadFull(entropySource, lastRandom[:])
			entropyMu.Unlock()
			if err != nil {
				return "", err
			}
		}
	} else {
		// 时间不同，生成新的随机部分
		entropyMu.Lock()
		_, err := io.ReadFull(entropySource, lastRandom[:])
		entropyMu.Unlock()
		if err != nil {
			return "", err
		}
		lastTime = ms
	}

	// 复制随机部分
	copy(id[6:], lastRandom[:])

	return id.String(), nil
}

// GeneratePrefixedULID 生成一个带前缀的 ULID
func GeneratePrefixedULID(prefix string) (string, error) {
	id, err := GenerateULID()
	if err != nil {
		return "", err
	}
	return prefix + id, nil
}

// MaxULID 返回可能的最大 ULID 值
func MaxULID() ULID {
	var id ULID
	for i := range id {
		id[i] = 0xFF
	}
	return id
}

// MinULID 返回可能的最小 ULID 值
func MinULID() ULID {
	var id ULID
	return id
}
