package utils

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"
)

// SSHConfig SSH连接配置
type SSHConfig struct {
	User     string // SSH用户名
	Password string // SSH密码
	KeyPath  string // SSH私钥路径
	Port     int    // SSH端口
	Timeout  int    // 连接超时时间(秒)
}

// DefaultSSHConfig 返回默认SSH配置
func DefaultSSHConfig() *SSHConfig {
	return &SSHConfig{
		User:     "root",
		Password: "",
		KeyPath:  "",
		Port:     22,
		Timeout:  10,
	}
}

// PrometheusResponse Prometheus API 响应结构
type PrometheusResponse struct {
	Status string `json:"status"`
	Data   struct {
		ResultType string `json:"resultType"`
		Result     []struct {
			Metric map[string]string `json:"metric"`
			Value  []any             `json:"value"`
		} `json:"result"`
	} `json:"data"`
}

// queryPrometheus 从 Prometheus 查询指标数据
func queryPrometheus(query string) (float64, error) {
	// 从环境变量获取 Prometheus 认证信息
	promUser := os.Getenv("PROMETHEUS_USER")
	promPassword := os.Getenv("PROMETHEUS_PASSWORD")

	// 如果环境变量未设置，使用默认值（仅用于开发环境）
	if promUser == "" {
		promUser = "admin-bao" // 实际部署时应从环境变量获取
	}

	// 构建请求 URL
	baseURL := "http://pw-prometheus.bpmax.cn/api/v1/query"
	data := url.Values{}
	data.Set("query", query)

	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", baseURL, strings.NewReader(data.Encode()))
	if err != nil {
		return 0, fmt.Errorf("创建 HTTP 请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Content-Length", strconv.Itoa(len(data.Encode())))

	// 设置基本认证
	if promUser != "" && promPassword != "" {
		req.SetBasicAuth(promUser, promPassword)
	}

	// 发送请求
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return 0, fmt.Errorf("发送 HTTP 请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return 0, fmt.Errorf("prometheus API 返回错误状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var promResp PrometheusResponse
	if err := json.NewDecoder(resp.Body).Decode(&promResp); err != nil {
		return 0, fmt.Errorf("解析 prometheus 响应失败: %w", err)
	}

	// 检查响应状态
	if promResp.Status != "success" {
		return 0, fmt.Errorf("prometheus 查询失败: %s", promResp.Status)
	}

	// 检查结果是否为空
	if len(promResp.Data.Result) == 0 {
		return 0, fmt.Errorf("prometheus 查询结果为空")
	}

	// 获取值
	valueArr := promResp.Data.Result[0].Value
	if len(valueArr) < 2 {
		return 0, fmt.Errorf("prometheus 查询结果格式错误")
	}

	// 第二个元素是值（字符串格式）
	valueStr, ok := valueArr[1].(string)
	if !ok {
		return 0, fmt.Errorf("prometheus 查询结果类型错误")
	}

	// 转换为 float64
	value, err := strconv.ParseFloat(valueStr, 64)
	if err != nil {
		return 0, fmt.Errorf("转换查询结果失败: %w", err)
	}

	return value, nil
}

// GetNodeCPUUsage 获取节点 CPU 最近5分钟的平均使用率
// 如果获取失败，返回100%使用率，使节点对外体现为不可用
func GetNodeCPUUsage(nodeInstance string) (float64, error) {
	// 构建 Prometheus 查询，计算最近5分钟的平均CPU使用率
	query := fmt.Sprintf("1 - sum(rate(node_cpu_seconds_total{instance=\"%s\", mode=\"idle\"}[15m])) / sum(rate(node_cpu_seconds_total{instance=\"%s\"}[5m]))",
		nodeInstance, nodeInstance)

	// 查询 Prometheus
	cpuUsage, err := queryPrometheus(query)
	if err != nil {
		log.Printf("获取CPU使用率失败，返回100%%使节点不可用: %v", err)
		return 1.0, nil // 返回100%使用率，使节点不可用
	}

	// 确保返回值在合理范围内 (0-1)
	if cpuUsage < 0 {
		cpuUsage = 0
	} else if cpuUsage > 1 {
		cpuUsage = 1
	}

	// 返回 CPU 使用率
	return cpuUsage, nil
}

// GetNodeMemoryUsage 获取节点内存最近15分钟的平均使用率
// nodeInstance 格式为 "hostname:port"，例如 "node73.yitaiyitai.com:1080"
// 如果获取失败，返回100%使用率，使节点对外体现为不可用
func GetNodeMemoryUsage(nodeInstance string) (float64, error) {
	// 构建 Prometheus 查询，计算最近5分钟的平均内存使用率
	// 使用 node_memory_MemAvailable_bytes 和 node_memory_MemTotal_bytes 的比率
	// 注意：这里我们使用两个独立的查询，然后在 PromQL 中计算它们的比率
	query := fmt.Sprintf(`
avg_over_time(node_memory_MemAvailable_bytes{instance="%s"}[5m]) /
avg_over_time(node_memory_MemTotal_bytes{instance="%s"}[5m])
`, nodeInstance, nodeInstance)

	// 将查询结果转换为内存使用率（1 - 可用/总量）
	availableRatio, err := queryPrometheus(query)
	if err != nil {
		log.Printf("获取内存使用率失败，返回100%%使节点不可用: %v", err)
		return 1.0, nil // 返回100%使用率，使节点不可用
	}

	// 计算内存使用率
	memUsage := 1.0 - availableRatio

	// 确保返回值在合理范围内 (0-1)
	if memUsage < 0 {
		memUsage = 0
	} else if memUsage > 1 {
		memUsage = 1
	}

	return memUsage, nil
}

// NodeHardwareInfo 节点硬件信息结构体
type NodeHardwareInfo struct {
	CPUCores float64 `json:"cpu_cores"` // CPU核心数
	MemoryMB int     `json:"memory_mb"` // 内存大小(MB)
	DiskMB   int     `json:"disk_mb"`   // /user目录磁盘大小(MB)
	Instance string  `json:"instance"`  // 实例标识
}

// GetNodeHardwareInfo 获取指定实例的硬件信息
// nodeInstance 格式为 "hostname:port"，例如 "node73.yitaiyitai.com:1080"
// 返回 CPU核数、内存MB数、/user目录磁盘MB数
// 如果某项资源获取失败，该项使用默认值0.1，不中断整个查询
func GetNodeHardwareInfo(nodeInstance string) (*NodeHardwareInfo, error) {
	info := &NodeHardwareInfo{
		Instance: nodeInstance,
	}

	// 1. 获取CPU核心数
	cpuQuery := fmt.Sprintf(`count(node_cpu_seconds_total{instance="%s", mode="idle"})`, nodeInstance)
	cpuCores, err := queryPrometheus(cpuQuery)
	if err != nil {
		log.Printf("获取CPU核心数失败，节点 %s，查询: %s，错误: %v", nodeInstance, cpuQuery, err)
		info.CPUCores = 0.1 // CPU核心数默认值0.1
	} else {
		log.Printf("成功获取CPU核心数，节点 %s: %.1f核心", nodeInstance, cpuCores)
		info.CPUCores = cpuCores // float64类型，直接赋值
	}

	// 2. 获取总内存大小(转换为MB)
	memoryQuery := fmt.Sprintf(`node_memory_MemTotal_bytes{instance="%s"} / 1024 / 1024`, nodeInstance)
	memoryMB, err := queryPrometheus(memoryQuery)
	if err != nil {
		log.Printf("获取内存大小失败，节点 %s，查询: %s，错误: %v", nodeInstance, memoryQuery, err)
		info.MemoryMB = 1 // 内存大小默认值1MB
	} else {
		log.Printf("成功获取内存大小，节点 %s: %dMB", nodeInstance, int(memoryMB))
		info.MemoryMB = int(memoryMB) // 转换为int类型
	}

	// 3. 获取/user目录的磁盘大小(转换为MB)
	diskQuery := fmt.Sprintf(`node_filesystem_size_bytes{instance="%s", mountpoint="/user"} / 1024 / 1024`, nodeInstance)
	diskMB, err := queryPrometheus(diskQuery)
	if err != nil {
		log.Printf("获取/user目录磁盘大小失败，节点 %s，查询: %s，错误: %v", nodeInstance, diskQuery, err)
		info.DiskMB = 1 // 磁盘大小默认值1MB
	} else {
		log.Printf("成功获取磁盘大小，节点 %s: %dMB", nodeInstance, int(diskMB))
		info.DiskMB = int(diskMB) // 转换为int类型
	}

	return info, nil
}

// GetNodeDiskUsage 获取节点/user目录最近5分钟的平均磁盘使用率
// nodeInstance 格式为 "hostname:port"，例如 "node73.yitaiyitai.com:1080"
// 返回磁盘使用率（0-1之间的浮点数，例如0.75表示75%使用率）
// 如果获取失败，返回100%使用率，使节点对外体现为不可用
func GetNodeDiskUsage(nodeInstance string) (float64, error) {
	// 构建 Prometheus 查询，计算最近5分钟的平均磁盘使用率
	// 使用 node_filesystem_avail_bytes 和 node_filesystem_size_bytes 的比率
	// 磁盘使用率 = 1 - (可用空间 / 总空间)
	query := fmt.Sprintf(`
1 - (
  avg_over_time(node_filesystem_avail_bytes{instance="%s", mountpoint="/user"}[5m]) /
  avg_over_time(node_filesystem_size_bytes{instance="%s", mountpoint="/user"}[5m])
)
`, nodeInstance, nodeInstance)

	// 查询 Prometheus
	diskUsage, err := queryPrometheus(query)
	if err != nil {
		log.Printf("获取磁盘使用率失败，返回100%%使节点不可用: %v", err)
		return 1.0, nil // 返回100%使用率，使节点不可用
	}

	// 确保返回值在合理范围内 (0-1)
	if diskUsage < 0 {
		diskUsage = 0
	} else if diskUsage > 1 {
		diskUsage = 1
	}

	return diskUsage, nil
}
