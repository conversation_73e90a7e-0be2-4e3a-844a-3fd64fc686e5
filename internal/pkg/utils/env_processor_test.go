package utils

import (
	"fmt"
	"strings"
	"testing"

	"github.com/zero-ops/service-system/internal/models"
)

// 辅助函数：获取map的所有键
func getMapKeys(m map[string]string) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

func TestProcessEnvironmentVariablesFromRequest(t *testing.T) {
	// 创建测试用的请求
	req := &models.WorkerDeployRequest{
		ServiceId:    "test-service-123",
		DomainPrefix: "myapp",
		DomainSuffix: ".example.com",
		Labels: []string{
			"SYS_TYPE=standard",
			"SYS_ENVS=APP_NAME:myapp|||PORT:8080|||DEBUG:true|||DATABASE_URL:mysql://user:pass@localhost:3306/db|||ES_SERVER:elasticsearch.example.com",
			"SYS_ENVS_TPL=ES_NAMESPACE:_{DOMAIN_NO_DOT}_|||ES_SERVER:http://_{ES_SERVER}_|||MYSQL_DATABASE:_{DOMAIN_NO_DOT}_|||RUNTIME_ENV:_{DOMAIN_NO_DOT}_|||SECRET_KEY:_{DOMAIN_NO_DOT}_^secret123",
		},
		CustomerEnvs: []string{
			"CUSTOM_VAR1=value1",
			"CUSTOM_VAR2=value with spaces",
			"CUSTOM_VAR3=value=with=equals",
		},
	}

	// 处理环境变量
	result, envMap, err := ProcessEnvironmentVariablesFromRequest(req)
	if err != nil {
		t.Fatalf("ProcessEnvironmentVariablesFromRequest failed: %v", err)
	}

	// 验证结果不为空
	if result == "" {
		t.Fatal("Expected non-empty result")
	}

	// 验证环境变量map不为空
	if envMap == nil {
		t.Fatal("Expected non-nil environment map")
	}

	// 验证环境变量map包含预期的变量（包括动态变量）
	expectedEnvKeys := []string{
		// 动态变量
		"SERVICE_ID", "DOMAIN", "DOMAIN_NO_DOT",
		// 模板变量
		"ES_NAMESPACE", "ES_SERVER", "MYSQL_DATABASE", "RUNTIME_ENV", "SECRET_KEY",
		// 用户自定义变量
		"CUSTOM_VAR1", "CUSTOM_VAR2", "CUSTOM_VAR3",
	}
	for _, key := range expectedEnvKeys {
		if _, exists := envMap[key]; !exists {
			t.Errorf("Expected environment variable %q not found in map", key)
		}
	}

	// 验证动态变量的具体值
	if envMap["SERVICE_ID"] != "test-service-123" {
		t.Errorf("Expected SERVICE_ID=test-service-123, got %q", envMap["SERVICE_ID"])
	}
	if envMap["DOMAIN"] != "myapp.example.com" {
		t.Errorf("Expected DOMAIN=myapp.example.com, got %q", envMap["DOMAIN"])
	}
	if envMap["DOMAIN_NO_DOT"] != "myapp_example_com" {
		t.Errorf("Expected DOMAIN_NO_DOT=myapp_example_com, got %q", envMap["DOMAIN_NO_DOT"])
	}

	// 验证结果包含预期的环境变量
	expectedVars := []string{
		"--env ES_NAMESPACE=\"myapp_example_com\"",
		"--env ES_SERVER=\"http://elasticsearch.example.com\"",
		"--env MYSQL_DATABASE=\"myapp_example_com\"",
		"--env RUNTIME_ENV=\"myapp_example_com\"",
		"--env SECRET_KEY=\"myapp_example_com^secret123\"",
		"--env CUSTOM_VAR1=\"value1\"",
		"--env CUSTOM_VAR2=\"value with spaces\"",
		"--env CUSTOM_VAR3=\"value=with=equals\"",
	}

	for _, expected := range expectedVars {
		if !strings.Contains(result, expected) {
			t.Errorf("Expected result to contain %q, but it didn't. Result: %s", expected, result)
		}
	}

	t.Logf("Generated environment string: %s", result)
}

func TestDockerCommandFormat(t *testing.T) {
	// 测试生成的环境变量字符串是否能正确用于Docker命令
	req := &models.WorkerDeployRequest{
		ServiceId:    "test-service-123",
		DomainPrefix: "myapp",
		DomainSuffix: ".example.com",
		Labels: []string{
			"SYS_ENVS=MYSQL_HOST:**************|||MYSQL_PORT:33068|||ES_SERVER:**************:9200",
			"SYS_ENVS_TPL=MYSQL_HOST:_{MYSQL_HOST}_|||MYSQL_PORT:_{MYSQL_PORT}_|||ES_SERVER:http://_{ES_SERVER}_|||DOMAIN:_{DOMAIN}_|||ENV_TAG:_{DOMAIN_NO_DOT}_",
		},
		CustomerEnvs: []string{
			"VARA=1456",
			"VARB=s d fd  24999",
			"PPORT=234",
		},
	}

	// 处理环境变量
	envString, envMap, err := ProcessEnvironmentVariablesFromRequest(req)
	if err != nil {
		t.Fatalf("ProcessEnvironmentVariablesFromRequest failed: %v", err)
	}

	// 验证环境变量字符串格式
	if !strings.HasPrefix(envString, " --env ") {
		t.Errorf("Environment string should start with ' --env ', got: %s", envString[:20])
	}

	// 验证没有重复的环境变量
	envParts := strings.Split(envString, " --env ")
	envKeys := make(map[string]bool)
	duplicates := []string{}

	for _, part := range envParts {
		if part == "" {
			continue
		}
		// 提取环境变量名
		if idx := strings.Index(part, "="); idx > 0 {
			key := part[:idx]
			if envKeys[key] {
				duplicates = append(duplicates, key)
			}
			envKeys[key] = true
		}
	}

	if len(duplicates) > 0 {
		t.Errorf("Found duplicate environment variables: %v", duplicates)
	}

	// 模拟Docker命令构建
	dockerCmd := fmt.Sprintf("docker run -d --name test-container%s test-image", envString)

	// 验证命令格式正确（没有连续的参数）
	if strings.Contains(dockerCmd, "--env--env") {
		t.Error("Docker command contains malformed environment arguments")
	}

	// 验证特定的环境变量存在
	expectedVars := []string{
		"MYSQL_HOST=\"**************\"",
		"MYSQL_PORT=\"33068\"",
		"ES_SERVER=\"http://**************:9200\"",
		"DOMAIN=\"myapp.example.com\"",
		"ENV_TAG=\"myapp_example_com\"",
		"VARA=\"1456\"",
		"VARB=\"s d fd  24999\"",
		"PPORT=\"234\"",
	}

	for _, expected := range expectedVars {
		if !strings.Contains(envString, expected) {
			t.Errorf("Expected environment variable %q not found in: %s", expected, envString)
		}
	}

	// 验证环境变量map的内容（包含动态变量）
	// envMap 包含: 动态变量(SERVICE_ID, DOMAIN, DOMAIN_NO_DOT) + Docker环境变量
	// 但是要注意可能有重复的键（比如DOMAIN在模板和动态变量中都存在）
	t.Logf("Environment map keys: %v", getMapKeys(envMap))
	t.Logf("Docker environment keys: %v", envKeys)

	// 验证动态变量存在
	dynamicVars := []string{"SERVICE_ID", "DOMAIN", "DOMAIN_NO_DOT"}
	for _, key := range dynamicVars {
		if _, exists := envMap[key]; !exists {
			t.Errorf("Expected dynamic variable %q not found in map", key)
		}
	}

	// 验证map中包含特定的环境变量
	expectedMapVars := map[string]string{
		"MYSQL_HOST": "**************",
		"MYSQL_PORT": "33068",
		"ES_SERVER":  "http://**************:9200",
		"DOMAIN":     "myapp.example.com",
		"ENV_TAG":    "myapp_example_com",
		"VARA":       "1456",
		"VARB":       "s d fd  24999",
		"PPORT":      "234",
	}

	for key, expectedValue := range expectedMapVars {
		if actualValue, exists := envMap[key]; !exists {
			t.Errorf("Expected environment variable %q not found in map", key)
		} else if actualValue != expectedValue {
			t.Errorf("Environment variable %q: expected %q, got %q", key, expectedValue, actualValue)
		}
	}

	t.Logf("Generated Docker command: %s", dockerCmd)
	t.Logf("Environment variables count: %d", len(envKeys))
	t.Logf("Environment map count: %d", len(envMap))
}

func TestReplaceScriptPlaceholders(t *testing.T) {
	// 创建测试用的环境变量map
	envMap := map[string]string{
		"MYSQL_HOST":     "**************",
		"MYSQL_PORT":     "33068",
		"MYSQL_DATABASE": "myapp_example_com",
		"ES_SERVER":      "http://**************:9200",
		"DOMAIN":         "myapp.example.com",
		"SERVICE_ID":     "test-service-123",
		"API_KEY":        "secret-key-123",
		"DEBUG_MODE":     "true",
	}

	// 测试脚本内容，包含不同格式的挖槽
	scriptContent := `#!/bin/bash
# 测试脚本，包含多种挖槽格式

# 标准shell变量格式
echo "MySQL Host: ${MYSQL_HOST}"
echo "MySQL Port: ${MYSQL_PORT}"
echo "Database: ${MYSQL_DATABASE}"

# 模板变量格式
echo "Domain: {{DOMAIN}}"
echo "Service ID: {{SERVICE_ID}}"
echo "ES Server: {{ES_SERVER}}"

# 自定义挖槽格式
echo "API Key: _{API_KEY}_"
echo "Debug Mode: _{DEBUG_MODE}_"

# 混合使用
mysql -h ${MYSQL_HOST} -P ${MYSQL_PORT} -D {{MYSQL_DATABASE}} -e "SELECT 1"
curl -H "X-API-Key: _{API_KEY}_" {{ES_SERVER}}/_cluster/health

# 不存在的变量（应该保持原样）
echo "Unknown: ${UNKNOWN_VAR}"
echo "Unknown: {{UNKNOWN_VAR}}"
echo "Unknown: _{UNKNOWN_VAR}_"
`

	// 执行挖槽替换
	result := ReplaceScriptPlaceholders(scriptContent, envMap)

	// 验证替换结果
	expectedReplacements := map[string]string{
		"${MYSQL_HOST}":      "**************",
		"${MYSQL_PORT}":      "33068",
		"${MYSQL_DATABASE}":  "myapp_example_com",
		"{{DOMAIN}}":         "myapp.example.com",
		"{{SERVICE_ID}}":     "test-service-123",
		"{{ES_SERVER}}":      "http://**************:9200",
		"_{API_KEY}_":        "secret-key-123",
		"_{DEBUG_MODE}_":     "true",
		"{{MYSQL_DATABASE}}": "myapp_example_com",
	}

	for placeholder, expectedValue := range expectedReplacements {
		if strings.Contains(result, placeholder) {
			t.Errorf("Placeholder %q was not replaced in script", placeholder)
		}
		if !strings.Contains(result, expectedValue) {
			t.Errorf("Expected value %q not found in result", expectedValue)
		}
	}

	// 验证不存在的变量保持原样
	unknownPlaceholders := []string{
		"${UNKNOWN_VAR}",
		"{{UNKNOWN_VAR}}",
		"_{UNKNOWN_VAR}_",
	}

	for _, placeholder := range unknownPlaceholders {
		if !strings.Contains(result, placeholder) {
			t.Errorf("Unknown placeholder %q should remain unchanged", placeholder)
		}
	}

	// 验证特定的替换结果
	expectedLines := []string{
		`echo "MySQL Host: **************"`,
		`echo "Domain: myapp.example.com"`,
		`echo "API Key: secret-key-123"`,
		`mysql -h ************** -P 33068 -D myapp_example_com -e "SELECT 1"`,
		`curl -H "X-API-Key: secret-key-123" http://**************:9200/_cluster/health`,
	}

	for _, expectedLine := range expectedLines {
		if !strings.Contains(result, expectedLine) {
			t.Errorf("Expected line not found in result: %q", expectedLine)
		}
	}

	t.Logf("Script placeholder replacement test completed successfully")
	t.Logf("Replaced %d environment variables", len(envMap))
}

func TestParseSysEnvs(t *testing.T) {
	labels := []string{
		"SYS_TYPE=standard",
		"SYS_ENVS=APP_NAME:myapp|||PORT:8080|||DEBUG:true|||EMPTY_VAR:",
	}

	result, err := parseSysEnvs(labels)
	if err != nil {
		t.Fatalf("parseSysEnvs failed: %v", err)
	}

	expected := map[string]string{
		"APP_NAME":  "myapp",
		"PORT":      "8080",
		"DEBUG":     "true",
		"EMPTY_VAR": "",
	}

	if len(result) != len(expected) {
		t.Fatalf("Expected %d variables, got %d", len(expected), len(result))
	}

	for key, expectedValue := range expected {
		if actualValue, exists := result[key]; !exists {
			t.Errorf("Expected key %q not found", key)
		} else if actualValue != expectedValue {
			t.Errorf("Expected %q=%q, got %q=%q", key, expectedValue, key, actualValue)
		}
	}
}

func TestParseSysEnvsTpl(t *testing.T) {
	labels := []string{
		"SYS_TYPE=standard",
		"SYS_ENVS_TPL=ES_NAMESPACE:_{DOMAIN_NO_DOT}_|||MYSQL_DATABASE:_{DOMAIN_NO_DOT}_|||SECRET_KEY:_{DOMAIN_NO_DOT}_^secret123",
	}

	result, err := parseSysEnvsTpl(labels)
	if err != nil {
		t.Fatalf("parseSysEnvsTpl failed: %v", err)
	}

	expected := map[string]string{
		"ES_NAMESPACE":   "_{DOMAIN_NO_DOT}_",
		"MYSQL_DATABASE": "_{DOMAIN_NO_DOT}_",
		"SECRET_KEY":     "_{DOMAIN_NO_DOT}_^secret123",
	}

	if len(result) != len(expected) {
		t.Fatalf("Expected %d variables, got %d", len(expected), len(result))
	}

	for key, expectedValue := range expected {
		if actualValue, exists := result[key]; !exists {
			t.Errorf("Expected key %q not found", key)
		} else if actualValue != expectedValue {
			t.Errorf("Expected %q=%q, got %q=%q", key, expectedValue, key, actualValue)
		}
	}
}

func TestParseCustomEnvs(t *testing.T) {
	customerEnvs := []string{
		"CUSTOM_VAR1=value1",
		"CUSTOM_VAR2=value with spaces",
		"CUSTOM_VAR3=value=with=equals",
		"EMPTY_VAR=",
		"NO_VALUE_VAR",
	}

	result, err := parseCustomEnvs(customerEnvs)
	if err != nil {
		t.Fatalf("parseCustomEnvs failed: %v", err)
	}

	expected := map[string]string{
		"CUSTOM_VAR1":  "value1",
		"CUSTOM_VAR2":  "value with spaces",
		"CUSTOM_VAR3":  "value=with=equals",
		"EMPTY_VAR":    "",
		"NO_VALUE_VAR": "",
	}

	if len(result) != len(expected) {
		t.Fatalf("Expected %d variables, got %d", len(expected), len(result))
	}

	for key, expectedValue := range expected {
		if actualValue, exists := result[key]; !exists {
			t.Errorf("Expected key %q not found", key)
		} else if actualValue != expectedValue {
			t.Errorf("Expected %q=%q, got %q=%q", key, expectedValue, key, actualValue)
		}
	}
}

func TestBuildEnvString(t *testing.T) {
	tplEnvs := map[string]string{
		"TPL_VAR1": "tpl_value1",
		"TPL_VAR2": "tpl_value2",
	}

	customEnvs := map[string]string{
		"CUSTOM_VAR1": "custom_value1",
		"CUSTOM_VAR2": "custom_value2",
	}

	result := buildEnvString(tplEnvs, customEnvs)

	// 验证结果包含所有变量
	expectedVars := []string{
		"--env TPL_VAR1=\"tpl_value1\"",
		"--env TPL_VAR2=\"tpl_value2\"",
		"--env CUSTOM_VAR1=\"custom_value1\"",
		"--env CUSTOM_VAR2=\"custom_value2\"",
	}

	for _, expected := range expectedVars {
		if !strings.Contains(result, expected) {
			t.Errorf("Expected result to contain %q, but it didn't. Result: %s", expected, result)
		}
	}

	// 验证模板变量在前，自定义变量在后
	tplIndex := strings.Index(result, "TPL_VAR1")
	customIndex := strings.Index(result, "CUSTOM_VAR1")
	if tplIndex == -1 || customIndex == -1 {
		t.Fatal("Could not find expected variables in result")
	}
	if tplIndex > customIndex {
		t.Error("Template variables should come before custom variables")
	}
}
