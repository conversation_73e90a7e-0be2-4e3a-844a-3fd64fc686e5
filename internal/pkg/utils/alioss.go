package utils

import (
	"bufio"
	"bytes"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/zero-ops/service-system/internal/models"
	"github.com/zero-ops/service-system/internal/pkg/interfaces"
	"github.com/zero-ops/service-system/internal/pkg/logger"
)

// buildOSSPath 构建OSS路径，避免双斜杠问题
func buildOSSPath(parts ...string) string {
	if len(parts) == 0 {
		return ""
	}

	var cleanParts []string
	for _, part := range parts {
		// 去除前后的斜杠
		cleaned := strings.Trim(part, "/")
		if cleaned != "" {
			cleanParts = append(cleanParts, cleaned)
		}
	}

	if len(cleanParts) == 0 {
		return ""
	}

	// 拼接路径，确保只有单个斜杠分隔
	return strings.Join(cleanParts, "/")
}

// buildOSSFullPath 构建完整的OSS路径，包括bucket
func buildOSSFullPath(bucketName string, pathParts ...string) string {
	if bucketName == "" {
		return ""
	}

	// 构建路径部分
	pathPart := buildOSSPath(pathParts...)

	// 如果路径为空，只返回bucket路径
	if pathPart == "" {
		return fmt.Sprintf("oss://%s/", bucketName)
	}

	// 返回完整路径
	return fmt.Sprintf("oss://%s/%s", bucketName, pathPart)
}

// ==================== 阿里云 OSS 配置 ====================

// AliOSSConfig 阿里云 OSS 配置
type AliOSSConfig struct {
	Endpoint        string // OSS endpoint
	AccessKeyID     string // 访问密钥ID
	AccessKeySecret string // 访问密钥Secret
	BucketName      string // 存储桶名称
	Region          string // 区域
	UploadBucket    string // 上传存储桶
	FilePath        string // 文件路径
	LogPath         string // 日志路径
}

// GetEndpoint 获取端点
func (c *AliOSSConfig) GetEndpoint() string { return c.Endpoint }

// GetAccessKeyID 获取访问密钥ID
func (c *AliOSSConfig) GetAccessKeyID() string { return c.AccessKeyID }

// GetAccessKeySecret 获取访问密钥Secret
func (c *AliOSSConfig) GetAccessKeySecret() string { return c.AccessKeySecret }

// GetBucketName 获取存储桶名称
func (c *AliOSSConfig) GetBucketName() string { return c.BucketName }

// GetRegion 获取区域
func (c *AliOSSConfig) GetRegion() string { return c.Region }

// GetFilePath 获取文件路径
func (c *AliOSSConfig) GetFilePath() string { return c.FilePath }

// GetLogPath 获取日志路径
func (c *AliOSSConfig) GetLogPath() string { return c.LogPath }

// Validate 验证配置
func (c *AliOSSConfig) Validate() error {
	if c.Endpoint == "" {
		return fmt.Errorf("OSSENDPOINT 不能为空")
	}
	if c.AccessKeyID == "" {
		return fmt.Errorf("ALIACCESSKEYID 不能为空")
	}
	if c.AccessKeySecret == "" {
		return fmt.Errorf("ALIACCESSKEYSECRET 不能为空")
	}
	if c.BucketName == "" {
		return fmt.Errorf("OSSBUCKET 不能为空")
	}
	return nil
}

// ParseAliOSSConfigFromEnvMap 从环境变量解析阿里云 OSS 配置
func ParseAliOSSConfigFromEnvMap(envMap map[string]string) (*AliOSSConfig, error) {
	config := &AliOSSConfig{
		Endpoint:        envMap["OSSENDPOINT"],
		AccessKeyID:     envMap["ALIACCESSKEYID"],
		AccessKeySecret: envMap["ALIACCESSKEYSECRET"],
		BucketName:      envMap["OSSBUCKET"],
		Region:          envMap["OSSREEGION"], // 这个变量长久以来就是错的，无法订正
		UploadBucket:    envMap["OSSUPLOADBUCKET"],
		FilePath:        envMap["OSSFILEPATH"],
		LogPath:         envMap["OSSLOGPATH"],
	}

	if err := config.Validate(); err != nil {
		return nil, err
	}

	// 设置默认值
	if config.LogPath == "" {
		config.LogPath = "oss-accesslog/"
	}
	if config.FilePath == "" {
		config.FilePath = "/"
	}

	logger.Info("阿里云OSS配置解析成功: bucket=%s, endpoint=%s, path=%s",
		config.BucketName, config.Endpoint, config.FilePath)

	return config, nil
}

// ==================== 阿里云 OSS 操作接口 ====================

// AliOSSProvider 阿里云 OSS 提供商
type AliOSSProvider struct{}

// TestConnection 测试连接（使用 OSSUtil 工具）
func (p *AliOSSProvider) TestConnection(config interfaces.OSSConfig) error {
	logger.Info("开始使用OSSUtil测试阿里云OSS连接, bucket: %s", config.GetBucketName())

	// 获取OSSUtil工具路径
	ossutilPath := p.getOSSUtilPath()

	// 检查工具是否存在
	if err := p.checkOSSUtilExists(ossutilPath); err != nil {
		return err
	}

	// 构建测试命令参数（列举bucket）
	args := []string{
		"du",
		fmt.Sprintf("oss://%s/oss-accesslog/", config.GetBucketName()),
		"--endpoint", config.GetEndpoint(),
		"--access-key-id", config.GetAccessKeyID(),
		"--access-key-secret", config.GetAccessKeySecret(),
	}

	logger.Info("执行阿里云OSSUtil连接测试命令: %s %v", ossutilPath, args)

	// 执行命令
	cmd := exec.Command(ossutilPath, args...)
	cmd.Dir = "."

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err := cmd.Run()
	if err != nil {
		logger.Error("阿里云OSSUtil连接测试失败: %v", err)
		logger.Error("错误输出: %s", stderr.String())
		return fmt.Errorf("OSS连接测试失败: %w, 错误输出: %s", err, stderr.String())
	}

	logger.Info("阿里云OSS连接测试成功, bucket: %s", config.GetBucketName())
	return nil
}

// GetBucketStatsWithUtil 使用 OSSUtil 工具获取存储统计
func (p *AliOSSProvider) GetBucketStatsWithUtil(config interfaces.OSSConfig) (*models.OSSUtilStats, error) {
	logger.Info("开始使用OSSUtil统计阿里云OSS磁盘使用量, bucket: %s, path: %s", config.GetBucketName(), config.GetFilePath())

	// 构建bucket路径
	bucketPath := fmt.Sprintf("oss://%s", config.GetBucketName())
	if config.GetFilePath() != "" && config.GetFilePath() != "/" {
		filePath := strings.TrimPrefix(config.GetFilePath(), "/")
		if filePath != "" {
			bucketPath = fmt.Sprintf("oss://%s/%s", config.GetBucketName(), filePath)
		}
	}

	// 获取OSSUtil工具路径
	ossutilPath := p.getOSSUtilPath()

	// 检查工具是否存在
	if err := p.checkOSSUtilExists(ossutilPath); err != nil {
		return nil, err
	}

	// 构建命令参数
	args := []string{
		"du",
		bucketPath,
		"--endpoint", config.GetEndpoint(),
		"--access-key-id", config.GetAccessKeyID(),
		"--access-key-secret", config.GetAccessKeySecret(),
	}

	logger.Info("执行阿里云OSSUtil命令: %s %v", ossutilPath, args)

	// 执行命令
	cmd := exec.Command(ossutilPath, args...)
	cmd.Dir = "."

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	startTime := time.Now()
	err := cmd.Run()
	executionTime := time.Since(startTime).String()

	if err != nil {
		logger.Error("阿里云OSSUtil命令执行失败: %v", err)
		logger.Error("错误输出: %s", stderr.String())
		return nil, fmt.Errorf("OSSUtil命令执行失败: %w, 错误输出: %s", err, stderr.String())
	}

	// 解析输出
	output := stdout.String()
	logger.Info("阿里云OSSUtil命令输出: %s", output)

	usage, err := p.parseOSSUtilDuOutput(output, bucketPath, executionTime)
	if err != nil {
		return nil, fmt.Errorf("解析OSSUtil输出失败: %w", err)
	}

	logger.Info("阿里云OSSUtil磁盘使用量统计完成: 文件数=%d, 总大小=%.2fMB, 执行时间=%s",
		usage.FileCount, usage.TotalSizeMB, usage.ExecutionTime)

	return usage, nil
}

// ==================== 日志处理功能 ====================

// DownloadLogFile 下载日志文件（使用 OSSUtil 工具）
func (p *AliOSSProvider) DownloadLogFile(config interfaces.OSSConfig, logPath string) (string, error) {
	logger.Info("开始使用OSSUtil下载阿里云OSS日志文件: %s", logPath)

	// 获取OSSUtil工具路径
	ossutilPath := p.getOSSUtilPath()

	// 检查工具是否存在
	if err := p.checkOSSUtilExists(ossutilPath); err != nil {
		return "", err
	}

	// 创建临时目录
	tempDir := "./dbdata/oss_logs"
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return "", fmt.Errorf("创建临时目录失败: %w", err)
	}

	// 构建源路径和目标路径
	// 需要包含 LogPath 前缀，因为 logPath 参数只是文件名
	fileName := strings.TrimPrefix(logPath, "/")

	// 使用 buildOSSFullPath 避免双斜杠问题
	sourcePath := buildOSSFullPath(config.GetBucketName(), config.GetLogPath(), fileName)
	localPath := filepath.Join(tempDir, filepath.Base(logPath))

	logger.Info("构建OSS日志文件路径: logPath='%s', fileName='%s', sourcePath='%s'",
		config.GetLogPath(), fileName, sourcePath)

	// 构建下载命令参数
	args := []string{
		"cp",
		sourcePath,
		localPath,
		"--endpoint", config.GetEndpoint(),
		"--access-key-id", config.GetAccessKeyID(),
		"--access-key-secret", config.GetAccessKeySecret(),
	}

	logger.Info("执行阿里云OSSUtil下载命令: %s %v", ossutilPath, args)

	// 执行命令
	cmd := exec.Command(ossutilPath, args...)
	cmd.Dir = "."

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err := cmd.Run()
	if err != nil {
		logger.Error("阿里云OSSUtil下载失败: %v", err)
		logger.Error("错误输出: %s", stderr.String())
		return "", fmt.Errorf("下载日志文件失败: %w, 错误输出: %s", err, stderr.String())
	}

	logger.Info("阿里云OSS日志文件下载成功: %s -> %s", logPath, localPath)
	return localPath, nil
}

// ListLogFiles 列举日志文件（使用 OSSUtil 工具）
// 返回本地不存在的日志文件列表，需要下载的文件
func (p *AliOSSProvider) ListLogFiles(config interfaces.OSSConfig, date time.Time) ([]string, error) {
	logger.Info("开始使用OSSUtil列举阿里云OSS日志文件, 日期: %s", date.Format("2006-01-02"))

	// 获取OSSUtil工具路径
	ossutilPath := p.getOSSUtilPath()

	// 检查工具是否存在
	if err := p.checkOSSUtilExists(ossutilPath); err != nil {
		return nil, err
	}

	// 确保本地 temp 目录存在
	tempDir := "./dbdata/oss_logs"
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		logger.Warn("创建临时目录失败: %v", err)
	}

	// 构建日志路径 - 使用 oss-accesslog 目录
	// 使用 buildOSSFullPath 避免双斜杠问题
	basePath := buildOSSFullPath(config.GetBucketName(), config.GetLogPath())
	// 确保路径以 / 结尾，但避免双斜杠
	var listPath string
	if strings.HasSuffix(basePath, "/") {
		listPath = basePath
	} else {
		listPath = basePath + "/"
	}

	// 构建日期过滤模式：{bucket名}2025-06-09*
	bucketName := config.GetBucketName()
	datePattern := fmt.Sprintf("*%s%s*", bucketName, date.Format("2006-01-02"))

	// 构建列举命令参数，使用 --include 过滤特定日期的文件
	args := []string{
		"ls",
		listPath,
		"--include", datePattern,
		"--endpoint", config.GetEndpoint(),
		"--access-key-id", config.GetAccessKeyID(),
		"--access-key-secret", config.GetAccessKeySecret(),
	}

	logger.Info("执行阿里云OSSUtil列举命令: %s %v", ossutilPath, args)
	logger.Info("过滤模式: %s", datePattern)

	// 执行命令
	cmd := exec.Command(ossutilPath, args...)
	cmd.Dir = "."

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err := cmd.Run()
	if err != nil {
		logger.Error("阿里云OSSUtil列举失败: %v", err)
		logger.Error("错误输出: %s", stderr.String())
		return nil, fmt.Errorf("列举日志文件失败: %w, 错误输出: %s", err, stderr.String())
	}

	// 解析输出，获取所有远程文件
	output := stdout.String()
	var allRemoteFiles []string

	lines := strings.Split(strings.TrimSpace(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 检查行内容是否包含 listPath，如果包含则为有效行
		if !strings.Contains(line, listPath) {
			continue // 不包含 listPath，跳过此行
		}

		// OSSUtil ls 输出格式通常是：
		// 2023-12-01 10:30:45    1024  oss://bucket/path/to/file
		fields := strings.Fields(line)
		if len(fields) >= 3 {
			// 获取最后一个字段，应该是完整的OSS路径
			fullPath := fields[len(fields)-1]
			if strings.HasPrefix(fullPath, "oss://") {
				// 提取文件路径（去掉 oss://bucket/ 前缀）
				bucketPrefix := listPath
				if strings.HasPrefix(fullPath, bucketPrefix) {
					filePath := strings.TrimPrefix(fullPath, bucketPrefix)
					// 只包含实际的文件，不包含目录
					if !strings.HasSuffix(filePath, "/") {
						allRemoteFiles = append(allRemoteFiles, filePath)
						logger.Info("找到远程文件: %s", filePath)
					}
				}
			}
		}
	}

	logger.Info("远程找到 %d 个日志文件", len(allRemoteFiles))

	// 检查本地文件，过滤出需要下载的文件
	var filesToDownload []string
	for _, remoteFile := range allRemoteFiles {
		// 提取文件名（去掉路径前缀）
		fileName := filepath.Base(remoteFile)
		localPath := filepath.Join(tempDir, fileName)

		// 检查本地文件是否存在
		if _, err := os.Stat(localPath); os.IsNotExist(err) {
			// 本地文件不存在，需要下载
			filesToDownload = append(filesToDownload, remoteFile)
			logger.Info("需要下载: %s (本地不存在)", fileName)
		} else {
			logger.Info("跳过下载: %s (本地已存在)", fileName)
		}
	}

	logger.Info("阿里云OSS日志文件列举完成, 远程 %d 个文件, 需要下载 %d 个文件",
		len(allRemoteFiles), len(filesToDownload))

	return filesToDownload, nil
}

// ParseLogFile 解析日志文件
func (p *AliOSSProvider) ParseLogFile(filePath string) ([]*models.OSSLogEntry, error) {
	logger.Info("开始解析阿里云OSS日志文件: %s", filePath)

	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开日志文件失败: %w", err)
	}
	defer file.Close()

	var entries []*models.OSSLogEntry
	scanner := bufio.NewScanner(file)

	// 阿里云OSS访问日志格式示例：
	// ************** - - [10/Jun/2025:00:05:41 +0800] "GET /svc014Q2S4S2158766Y7J6P3J1J2Y/upload/admin/427774471/2023/09/14/233017746343b587313c3e1addcdc25142f0ea.png HTTP/1.1" 200 69124 8 "https://yygl.shuxinyc.com/" "Mozilla/5.0..." "pw-garden.oss-cn-shanghai.aliyuncs.com" "68470655E5C23A3431D01D67" "true" "-" "GetObject" "pw-garden" "hptfile%2Fupload%2Fadmin%2F427774471%2F2023%2F09%2F14%2F233017746343b587313c3e1addcdc25142f0ea.png" 69124 7 "-" 843 "1889183579840995" - "-" "standard" "-" "-" "-"
	// 格式：IP - - [时间] "方法 路径 协议" 状态码 响应大小 处理时间 "Referer" "User-Agent" "Host" "RequestID" "是否命中" "-" "操作" "Bucket" "对象键" 对象大小 处理时间 "-" 其他字段...
	logPattern := regexp.MustCompile(`(\S+)\s+-\s+-\s+\[([^\]]+)\]\s+"(\S+)\s+([^"]+?)\s+[^"]+"\s+(\d+)\s+(\d+)\s+\d+\s+"[^"]*"\s+"[^"]*"\s+"[^"]*"\s+"[^"]*"\s+"[^"]*"\s+"[^"]*"\s+"[^"]*"\s+"[^"]*"\s+"([^"]*?)"\s+(\d+)`)

	for scanner.Scan() {
		line := scanner.Text()
		matches := logPattern.FindStringSubmatch(line)

		if len(matches) >= 9 {
			// 解析时间
			timeStr := matches[2]
			timestamp, err := time.Parse("02/Jan/2006:15:04:05 -0700", timeStr)
			if err != nil {
				logger.Warn("解析时间失败: %s, 跳过该行", timeStr)
				continue
			}

			// 解析文件大小 (对象大小)
			sizeStr := matches[8]
			size, err := strconv.ParseInt(sizeStr, 10, 64)
			if err != nil {
				logger.Warn("解析文件大小失败: %s, 跳过该行", sizeStr)
				continue
			}

			// 解析状态码
			statusStr := matches[5]
			statusCode, err := strconv.Atoi(statusStr)
			if err != nil {
				logger.Warn("解析状态码失败: %s, 跳过该行", statusStr)
				continue
			}

			// 只统计成功的下载请求
			if statusCode == 200 && matches[3] == "GET" {
				entry := &models.OSSLogEntry{
					Timestamp:  timestamp,
					ClientIP:   matches[1],
					Method:     matches[3],
					FilePath:   matches[4],
					StatusCode: statusCode,
					ObjectSize: size,
				}
				entries = append(entries, entry)
			}
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("读取日志文件失败: %w", err)
	}

	logger.Info("阿里云OSS日志文件解析完成, 解析出 %d 条有效记录", len(entries))
	return entries, nil
}

// ==================== 工具函数 ====================

// getOSSUtilPath 根据运行时环境获取 OSSUtil 工具路径
func (p *AliOSSProvider) getOSSUtilPath() string {
	baseDir := "./dbdata"

	// 根据操作系统选择对应的工具
	switch runtime.GOOS {
	case "linux":
		return filepath.Join(baseDir, "ossutil64-linux")
	case "darwin":
		return filepath.Join(baseDir, "ossutilmac64")
	case "windows":
		return filepath.Join(baseDir, "ossutil64.exe")
	default:
		// 默认使用 Linux 版本
		return filepath.Join(baseDir, "ossutil64-linux")
	}
}

// checkOSSUtilExists 检查 OSSUtil 工具是否存在
func (p *AliOSSProvider) checkOSSUtilExists(path string) error {
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return fmt.Errorf("OSSUtil 工具不存在: %s", path)
	}
	return nil
}

// parseOSSUtilDuOutput 解析 OSSUtil du 命令的输出
func (p *AliOSSProvider) parseOSSUtilDuOutput(output, bucketPath, executionTime string) (*models.OSSUtilStats, error) {
	lines := strings.Split(strings.TrimSpace(output), "\n")
	logger.Info("开始解析OSS统计输出，共 %d 行", len(lines))

	if len(lines) == 0 {
		return nil, fmt.Errorf("OSSUtil输出为空")
	}

	var totalSizeBytes int64
	var objectCount int64
	var foundCount, foundSize bool

	// 逐行读取，只处理包含目标关键字的行
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// logger.Info("处理第 %d 行: %s", i+1, line) // 调试时可启用

		// 1. 检查是否包含 total object count
		if strings.Contains(line, "total object count") {
			re := regexp.MustCompile(`total object count:\s*(\d+)`)
			matches := re.FindStringSubmatch(line)
			if len(matches) > 1 {
				if count, err := strconv.ParseInt(matches[1], 10, 64); err == nil {
					objectCount = count
					foundCount = true
					logger.Info("✅ 解析到文件数量: %d", objectCount)
				} else {
					logger.Warn("解析文件数量失败: %v", err)
				}
			}
		}

		// 2. 检查是否包含 total object sum size
		if strings.Contains(line, "total object sum size") {
			re := regexp.MustCompile(`total object sum size:\s*(\d+)`)
			matches := re.FindStringSubmatch(line)
			if len(matches) > 1 {
				if size, err := strconv.ParseInt(matches[1], 10, 64); err == nil {
					totalSizeBytes = size
					foundSize = true
					logger.Info("✅ 解析到总大小: %d 字节 (%.2f MB)", totalSizeBytes, float64(totalSizeBytes)/(1024*1024))
				} else {
					logger.Warn("解析总大小失败: %v", err)
				}
			}
			continue // 处理完这行，继续下一行
		}

		// 3. 其他行跳过
		// logger.Info("跳过第 %d 行（不包含目标关键字）", i+1) // 调试时可启用
	}

	// 验证是否解析到必要的数据
	if !foundCount || !foundSize {
		return nil, fmt.Errorf("解析失败: 文件数量=%t, 总大小=%t", foundCount, foundSize)
	}

	if totalSizeBytes == 0 || objectCount == 0 {
		return nil, fmt.Errorf("解析到无效数据: 文件数量=%d, 总大小=%d", objectCount, totalSizeBytes)
	}

	// 3. 转换总大小为 MB
	totalSizeMB := float64(totalSizeBytes) / (1024 * 1024)

	// 4. 构建结果
	usage := &models.OSSUtilStats{
		TotalSize:     totalSizeBytes,
		TotalSizeMB:   totalSizeMB,
		FileCount:     objectCount,
		ObjectCount:   objectCount,
		BucketPath:    bucketPath,
		ExecutionTime: executionTime,
	}

	logger.Info("🎉 OSS统计解析成功: 文件数=%d, 总大小=%.2fMB (%.2fGB)",
		objectCount, totalSizeMB, totalSizeMB/1024)

	return usage, nil
}
