# Zero Ops Service System - Development Configuration
# 开发环境配置

# 通知系统配置
notification:
  # 飞书通知配置 - 开发环境启用
  feishu:
    enabled: true
    webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/ebe8099d-fe6b-4283-b0f1-3a5ae803cbc9"
    user_map:
      zerozen: "ou_1234567890"
  
  # 微信通知配置 - 开发环境禁用
  wechat:
    enabled: false
    webhook_url: ""
    app_id: ""
    secret: ""
  
  # 钉钉通知配置 - 开发环境禁用
  dingtalk:
    enabled: false
    webhook_url: ""
    access_token: ""
  
  # 邮件通知配置 - 开发环境禁用
  email:
    enabled: false
    smtp_host: ""
    smtp_port: 587
    username: ""
    password: ""
    from_name: "Zero Ops System [DEV]"
    to_addresses: []

# 服务配置
service:
  port: 8080
  host: "127.0.0.1"  # 开发环境只监听本地
  
# 数据库配置
database:
  path: "./data/dev-service.db"  # 开发环境数据库
  
# 日志配置
logging:
  level: "debug"  # 开发环境使用 debug 级别
  file: "./logs/dev-service.log"
