# Business ID Migration Test Plan

## 修改总结

我们成功将 `service_resource_usage` 表从基于 `service_id` 的设计迁移到基于 `business_id` 的设计。

### 主要修改内容

#### 1. 数据库层修改
- **表结构**：`service_id` → `business_id` (TEXT NOT NULL UNIQUE)
- **删除迁移代码**：移除了 `oss_network_traffic_by_day` 字段的 ALTER TABLE 代码

#### 2. 模型层修改
- **ServiceResourceUsage 结构体**：`ServiceID` → `BusinessID`
- **JSON 和数据库标签**：`service_id` → `business_id`

#### 3. Repository 层修改
- **方法重命名**：
  - `GetByServiceID` → `GetByBusinessID`
  - `GetByServiceIDs` → `GetByBusinessIDs`
  - `UpdateByServiceID` → `UpdateByBusinessID`
  - `UpsertByServiceID` → `UpsertByBusinessID`
  - `DeleteByServiceID` → `DeleteByBusinessID`
- **SQL 查询更新**：所有查询中的 `service_id` 字段改为 `business_id`

#### 4. 业务逻辑层修改
- **新增方法**：`getBusinessIDFromServiceID` 和 `parseCustomEnvs`
- **流量统计逻辑**：所有 OSS 流量统计现在基于 `business_id` 进行
- **资源使用记录**：创建和更新都使用 `business_id`

#### 5. API 层修改
- **向后兼容**：`CheckResourceStatus` 同时支持 `service_ids` 和 `business_ids` 参数
- **新增方法**：`GetResourceStatusByBusinessIDs` 专门处理业务级查询
- **响应数据**：包含 `business_id` 字段，保持 `service_id` 字段向后兼容

### 关键特性

#### 1. 向后兼容性
- API 接口同时支持 `service_ids` 和 `business_ids` 参数
- 响应数据包含两个字段，确保现有调用方不受影响

#### 2. 数据一致性
- 所有流量统计按 `business_id` 聚合
- OSS 存储路径已经使用 `business_id`，保持一致性

#### 3. 性能优化
- 批量查询支持 `business_id` 列表
- 避免重复的 `serviceID` → `businessID` 转换

## 测试建议

### 1. 单元测试
```bash
# 测试编译
go build -o /tmp/test-build ./cmd/server

# 测试数据库连接
# 启动服务并检查表结构是否正确创建
```

### 2. API 测试
```bash
# 测试向后兼容性
curl "http://localhost:8080/api/services/resource-status?service_ids=svc001,svc002"

# 测试新的 business_ids 参数
curl "http://localhost:8080/api/services/resource-status?business_ids=app1tenant1,app2tenant2"
```

### 3. 数据验证
- 验证 `business_id` 的计算逻辑：`SAAS_APP_ID` + `SAAS_TANENT_KEY`
- 验证流量统计是否正确聚合到 `business_id`
- 验证资源使用记录的创建和更新

## 风险评估

### 低风险
- ✅ 编译成功，无语法错误
- ✅ 保持向后兼容性
- ✅ 数据库表结构清晰

### 需要注意
- 🔍 确保所有环境变量 `SAAS_APP_ID` 和 `SAAS_TANENT_KEY` 正确设置
- 🔍 验证 `business_id` 计算的唯一性
- 🔍 监控流量统计的准确性

## 部署建议

1. **测试环境验证**：先在测试环境部署并验证功能
2. **数据备份**：部署前备份现有数据
3. **监控部署**：部署后监控日志和 API 响应
4. **逐步迁移**：建议调用方逐步迁移到 `business_ids` 参数

## 成功指标

- ✅ 服务正常启动
- ✅ 数据库表正确创建
- ✅ API 接口正常响应
- ✅ 流量统计正确聚合
- ✅ 向后兼容性保持
